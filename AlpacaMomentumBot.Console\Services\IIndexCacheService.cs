namespace SmaTrendFollower.Services;

/// <summary>
/// Service for caching index bar data in SQLite to reduce API calls to Polygon.
/// Implements nightly caching strategy to avoid re-downloading unchanged history.
/// </summary>
public interface IIndexCacheService
{
    /// <summary>
    /// Gets cached index bars for a symbol within the specified date range.
    /// Returns only the bars that are available in cache.
    /// </summary>
    /// <param name="symbol">Index symbol (e.g., "I:SPX", "I:VIX")</param>
    /// <param name="startDate">Start date for the range</param>
    /// <param name="endDate">End date for the range</param>
    /// <returns>Cached index bars within the date range</returns>
    Task<IEnumerable<IndexBar>> GetCachedBarsAsync(string symbol, DateTime startDate, DateTime endDate);

    /// <summary>
    /// Stores index bars in the cache for future retrieval.
    /// </summary>
    /// <param name="symbol">Index symbol</param>
    /// <param name="indexBars">Index bars to cache</param>
    Task CacheBarsAsync(string symbol, IEnumerable<IndexBar> indexBars);

    /// <summary>
    /// Determines the date range that needs to be fetched from the API.
    /// Returns null if all requested data is available in cache.
    /// </summary>
    /// <param name="symbol">Index symbol</param>
    /// <param name="requestedStartDate">Requested start date</param>
    /// <param name="requestedEndDate">Requested end date</param>
    /// <returns>Date range to fetch from API, or null if all data is cached</returns>
    Task<(DateTime startDate, DateTime endDate)?> GetMissingDateRangeAsync(string symbol, DateTime requestedStartDate, DateTime requestedEndDate);

    /// <summary>
    /// Gets the latest cached date for a symbol.
    /// </summary>
    /// <param name="symbol">Index symbol</param>
    /// <returns>Latest cached date, or null if no data is cached</returns>
    Task<DateTime?> GetLatestCachedDateAsync(string symbol);

    /// <summary>
    /// Checks if the cache is fresh enough for the given symbol and date range.
    /// Cache is considered fresh if it contains data up to the previous trading day.
    /// </summary>
    /// <param name="symbol">Index symbol</param>
    /// <param name="requestedEndDate">Requested end date</param>
    /// <returns>True if cache is fresh, false if needs update</returns>
    Task<bool> IsCacheFreshAsync(string symbol, DateTime requestedEndDate);

    /// <summary>
    /// Performs cache maintenance - removes old data and optimizes storage.
    /// </summary>
    /// <param name="retainDays">Number of days of data to retain (default: 365)</param>
    Task PerformMaintenanceAsync(int retainDays = 365);

    /// <summary>
    /// Ensures the cache database is initialized and ready for use.
    /// </summary>
    Task InitializeCacheAsync();
}
