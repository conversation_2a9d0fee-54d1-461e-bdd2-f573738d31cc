using Serilog;

namespace SmaTrendFollower.Services;

public sealed class TradingService : ITradingService
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly IRiskManager _riskManager;
    private readonly IPortfolioGate _portfolioGate;
    private readonly ITradeExecutor _executor;
    private readonly IStopManager _stopManager;

    public TradingService(
        ISignalGenerator signalGenerator,
        IRiskManager riskManager,
        IPortfolioGate portfolioGate,
        ITradeExecutor executor,
        IStopManager stopManager)
    {
        _signalGenerator = signalGenerator;
        _riskManager = riskManager;
        _portfolioGate = portfolioGate;
        _executor = executor;
        _stopManager = stopManager;
    }

    public async Task ExecuteCycleAsync(CancellationToken cancellationToken = default)
    {
        // First, update trailing stops for existing positions (capital preservation)
        Log.Information("Updating trailing stops for existing positions");
        await _stopManager.UpdateTrailingStopsAsync(cancellationToken);

        // Check if we should trade today (SPY SMA200 check)
        if (!await _portfolioGate.ShouldTradeAsync())
        {
            Log.Information("Portfolio gate blocked trading - SPY below SMA200");
            return;
        }

        // Generate trading signals
        var signals = await _signalGenerator.RunAsync(10);

        foreach (var signal in signals)
        {
            var quantity = await _riskManager.CalculateQuantityAsync(signal);
            if (quantity > 0)
            {
                await _executor.ExecuteTradeAsync(signal, quantity);
            }
        }

        Log.Information("Trading cycle completed");
    }
}
