using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for pre-populating cache with frequently used symbols during off-market hours
/// Optimizes performance by ensuring commonly requested data is always available locally
/// </summary>
public sealed class CacheWarmingService : ICacheWarmingService
{
    private readonly IMarketDataService _marketDataService;
    private readonly IStockBarCacheService _cacheService;
    private readonly IMarketSessionGuard _marketGuard;
    private readonly ITimeProvider _timeProvider;
    private readonly ILogger<CacheWarmingService> _logger;
    private readonly CacheWarmingConfig _config;

    private DateTime? _lastWarmingRun;
    private readonly SemaphoreSlim _warmingSemaphore = new(1, 1);

    public CacheWarmingService(
        IMarketDataService marketDataService,
        IStockBarCacheService cacheService,
        IMarketSessionGuard marketGuard,
        ITimeProvider timeProvider,
        ILogger<CacheWarmingService> logger,
        IOptions<CacheWarmingConfig>? config = null)
    {
        _marketDataService = marketDataService;
        _cacheService = cacheService;
        _marketGuard = marketGuard;
        _timeProvider = timeProvider;
        _logger = logger;
        _config = config?.Value ?? CacheWarmingConfig.Default;
    }

    public async Task<(int symbolsWarmed, int totalBarsCached)> WarmEssentialSymbolsAsync(int daysOfHistory = 365)
    {
        _logger.LogInformation("Starting cache warming for essential symbols");

        var startDate = _timeProvider.UtcNow.AddDays(-daysOfHistory);
        var endDate = _timeProvider.UtcNow;

        return await WarmSymbolsAsync(_config.EssentialSymbols, startDate, endDate, "essential");
    }

    public async Task<(int symbolsWarmed, int totalBarsCached)> WarmUniverseSymbolsAsync(IEnumerable<string> symbols, int daysOfHistory = 250)
    {
        _logger.LogInformation("Starting cache warming for universe symbols");

        var symbolList = symbols.ToList();
        var startDate = _timeProvider.UtcNow.AddDays(-daysOfHistory);
        var endDate = _timeProvider.UtcNow;

        return await WarmSymbolsAsync(symbolList, startDate, endDate, "universe");
    }

    public async Task<(int symbolsUpdated, int newBarsCached)> IncrementalWarmAsync(IEnumerable<string> symbols)
    {
        _logger.LogInformation("Starting incremental cache warming");

        var symbolList = symbols.ToList();
        var endDate = _timeProvider.UtcNow;
        var symbolsUpdated = 0;
        var totalNewBars = 0;

        // Process in batches to avoid overwhelming APIs
        for (int i = 0; i < symbolList.Count; i += _config.BatchSize)
        {
            var batch = symbolList.Skip(i).Take(_config.BatchSize);
            var batchTasks = batch.Select(async symbol =>
            {
                try
                {
                    // Check what data we already have
                    var latestCached = await _cacheService.GetLatestCachedDateAsync(symbol, "Day");
                    var startDate = latestCached?.AddDays(1) ?? endDate.AddDays(-30); // Default to 30 days if no cache

                    if (startDate >= endDate)
                    {
                        _logger.LogDebug("Cache for {Symbol} is up to date", symbol);
                        return new { Symbol = symbol, NewBars = 0, Success = true };
                    }

                    // Fetch missing data
                    var bars = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                    var newBarsCount = bars.Items.Count();

                    _logger.LogDebug("Warmed {Symbol}: {NewBars} new bars from {StartDate:yyyy-MM-dd}", 
                        symbol, newBarsCount, startDate);

                    return new { Symbol = symbol, NewBars = newBarsCount, Success = true };
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to warm cache for symbol {Symbol}", symbol);
                    return new { Symbol = symbol, NewBars = 0, Success = false };
                }
            });

            var batchResults = await Task.WhenAll(batchTasks);
            var successfulResults = batchResults.Where(r => r.Success);

            symbolsUpdated += successfulResults.Count();
            totalNewBars += successfulResults.Sum(r => r.NewBars);

            // Small delay between batches to be respectful to APIs
            if (i + _config.BatchSize < symbolList.Count)
            {
                await Task.Delay(1000);
            }
        }

        _logger.LogInformation("Incremental warming completed: {SymbolsUpdated} symbols updated, {NewBars} new bars cached",
            symbolsUpdated, totalNewBars);

        return (symbolsUpdated, totalNewBars);
    }

    public async Task<bool> ShouldRunWarmingAsync()
    {
        try
        {
            // Check if market is closed
            var isMarketOpen = await _marketGuard.IsMarketOpenAsync();
            if (isMarketOpen)
            {
                _logger.LogDebug("Market is open, skipping cache warming");
                return false;
            }

            // Check if enough time has passed since last warming
            if (_lastWarmingRun.HasValue)
            {
                var timeSinceLastRun = _timeProvider.UtcNow - _lastWarmingRun.Value;
                if (timeSinceLastRun < _config.WarmingInterval)
                {
                    _logger.LogDebug("Not enough time since last warming run ({TimeSince})", timeSinceLastRun);
                    return false;
                }
            }

            // Check if we're in the preferred time window
            var currentTime = _timeProvider.UtcNow.TimeOfDay;
            var preferredStart = _config.PreferredStartTime.ToTimeSpan();
            var preferredEnd = _config.PreferredEndTime.ToTimeSpan();

            bool inPreferredWindow;
            if (preferredStart > preferredEnd) // Overnight window (e.g., 18:00 to 06:00)
            {
                inPreferredWindow = currentTime >= preferredStart || currentTime <= preferredEnd;
            }
            else // Same day window
            {
                inPreferredWindow = currentTime >= preferredStart && currentTime <= preferredEnd;
            }

            if (!inPreferredWindow)
            {
                _logger.LogDebug("Outside preferred warming window");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if warming should run");
            return false;
        }
    }

    public async Task<CacheWarmingStats> GetWarmingStatsAsync()
    {
        try
        {
            var cacheStats = await _cacheService.GetCacheStatsAsync();
            var totalSymbols = cacheStats.Count;
            var totalBars = cacheStats.Values.Sum(s => s.BarCount);

            var recentSymbols = cacheStats.Values
                .OrderByDescending(s => s.LastUpdated)
                .Take(10)
                .Select(s => s.Symbol)
                .ToArray();

            return new CacheWarmingStats(
                _lastWarmingRun,
                totalSymbols,
                totalBars,
                TimeSpan.Zero, // Would need to track this separately
                0, // Would need to track failed symbols
                recentSymbols
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting warming statistics");
            return new CacheWarmingStats(null, 0, 0, TimeSpan.Zero, 0, Array.Empty<string>());
        }
    }

    public async Task RunScheduledWarmingAsync(CancellationToken cancellationToken = default)
    {
        if (!await _warmingSemaphore.WaitAsync(100, cancellationToken))
        {
            _logger.LogDebug("Cache warming already in progress, skipping");
            return;
        }

        try
        {
            if (!await ShouldRunWarmingAsync())
            {
                return;
            }

            _logger.LogInformation("Starting scheduled cache warming");
            var startTime = _timeProvider.UtcNow;

            // Warm essential symbols first
            var (essentialWarmed, essentialBars) = await WarmEssentialSymbolsAsync(_config.EssentialSymbolsDays);

            // Then do incremental warming for other symbols
            var allCacheStats = await _cacheService.GetCacheStatsAsync();
            var existingSymbols = allCacheStats.Keys.Select(k => k.Split('_')[0]).Distinct();
            var (incrementalWarmed, incrementalBars) = await IncrementalWarmAsync(existingSymbols);

            var duration = _timeProvider.UtcNow - startTime;
            _lastWarmingRun = startTime;

            _logger.LogInformation("Scheduled cache warming completed in {Duration}: " +
                "{EssentialWarmed} essential symbols ({EssentialBars} bars), " +
                "{IncrementalWarmed} incremental symbols ({IncrementalBars} bars)",
                duration, essentialWarmed, essentialBars, incrementalWarmed, incrementalBars);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during scheduled cache warming");
        }
        finally
        {
            _warmingSemaphore.Release();
        }
    }

    private async Task<(int symbolsWarmed, int totalBarsCached)> WarmSymbolsAsync(
        IEnumerable<string> symbols, 
        DateTime startDate, 
        DateTime endDate, 
        string category)
    {
        var symbolList = symbols.ToList();
        var symbolsWarmed = 0;
        var totalBars = 0;

        _logger.LogInformation("Warming {Category} symbols: {Count} symbols from {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd}",
            category, symbolList.Count, startDate, endDate);

        // Process symbols with controlled concurrency
        using var semaphore = new SemaphoreSlim(_config.MaxConcurrentSymbols, _config.MaxConcurrentSymbols);
        var tasks = symbolList.Select(async symbol =>
        {
            await semaphore.WaitAsync();
            try
            {
                var bars = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                var barCount = bars.Items.Count();

                _logger.LogDebug("Warmed {Symbol}: {BarCount} bars", symbol, barCount);

                return new { Symbol = symbol, BarCount = barCount, Success = true };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to warm {Category} symbol {Symbol}", category, symbol);
                return new { Symbol = symbol, BarCount = 0, Success = false };
            }
            finally
            {
                semaphore.Release();
            }
        });

        var results = await Task.WhenAll(tasks);
        var successfulResults = results.Where(r => r.Success);

        symbolsWarmed = successfulResults.Count();
        totalBars = successfulResults.Sum(r => r.BarCount);

        _logger.LogInformation("Completed warming {Category} symbols: {SymbolsWarmed}/{TotalSymbols} successful, {TotalBars} bars cached",
            category, symbolsWarmed, symbolList.Count, totalBars);

        return (symbolsWarmed, totalBars);
    }
}
