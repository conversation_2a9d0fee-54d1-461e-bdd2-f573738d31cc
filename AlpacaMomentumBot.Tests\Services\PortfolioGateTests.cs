using SmaTrendFollower.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class PortfolioGateTests
{
    private readonly Mock<IAlpacaClientFactory> _mockClientFactory;
    private readonly Mock<IAlpacaDataClient> _mockDataClient;
    private readonly Mock<ILogger<PortfolioGate>> _mockLogger;
    private readonly PortfolioGate _portfolioGate;

    public PortfolioGateTests()
    {
        _mockClientFactory = new Mock<IAlpacaClientFactory>();
        _mockDataClient = new Mock<IAlpacaDataClient>();
        _mockLogger = new Mock<ILogger<PortfolioGate>>();
        
        _mockClientFactory.Setup(x => x.CreateDataClient()).Returns(_mockDataClient.Object);
        
        _portfolioGate = new PortfolioGate(_mockClientFactory.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task ShouldTradeAsync_WhenSpyAboveSma200_ReturnsTrue()
    {
        // Arrange
        var bars = CreateMockBars(currentPrice: 450m, sma200Price: 400m);
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(bars);
        
        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(It.IsAny<HistoricalBarsRequest>(), default))
            .ReturnsAsync(mockResponse.Object);

        // Act
        var result = await _portfolioGate.ShouldTradeAsync();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ShouldTradeAsync_WhenSpyBelowSma200_ReturnsFalse()
    {
        // Arrange
        var bars = CreateMockBars(currentPrice: 380m, sma200Price: 400m);
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(bars);
        
        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(It.IsAny<HistoricalBarsRequest>(), default))
            .ReturnsAsync(mockResponse.Object);

        // Act
        var result = await _portfolioGate.ShouldTradeAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task ShouldTradeAsync_WithInsufficientData_ReturnsFalse()
    {
        // Arrange
        var bars = CreateMockBars(currentPrice: 450m, sma200Price: 400m, barCount: 150); // Less than 200
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(bars);
        
        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(It.IsAny<HistoricalBarsRequest>(), default))
            .ReturnsAsync(mockResponse.Object);

        // Act
        var result = await _portfolioGate.ShouldTradeAsync();

        // Assert
        result.Should().BeFalse();
    }

    private static List<IBar> CreateMockBars(decimal currentPrice, decimal sma200Price, int barCount = 250)
    {
        var bars = new List<IBar>();
        
        for (int i = 0; i < barCount; i++)
        {
            var mockBar = new Mock<IBar>();
            
            // Create a price pattern that results in the desired SMA200
            var price = i < barCount - 1 ? sma200Price : currentPrice;
            
            mockBar.Setup(x => x.Close).Returns(price);
            mockBar.Setup(x => x.Open).Returns(price * 0.99m);
            mockBar.Setup(x => x.High).Returns(price * 1.01m);
            mockBar.Setup(x => x.Low).Returns(price * 0.98m);
            mockBar.Setup(x => x.Volume).Returns(1000000);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-barCount + i));
            
            bars.Add(mockBar.Object);
        }
        
        return bars;
    }
}
