using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Implementation of real-time streaming data service combining Alpaca and Polygon websockets
/// </summary>
public sealed class StreamingDataService : IStreamingDataService
{
    private readonly IAlpacaClientFactory _alpacaFactory;
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly ILogger<StreamingDataService> _logger;

    private IAlpacaStreamingClient? _alpacaStreamingClient;
    private IAlpacaDataStreamingClient? _alpacaDataStreamingClient;
    private IPolygonWebSocketClient? _polygonWebSocketClient;
    private readonly HashSet<string> _subscribedQuoteSymbols = new();
    private readonly HashSet<string> _subscribedBarSymbols = new();
    private readonly HashSet<string> _subscribedIndexSymbols = new();
    private bool _tradeUpdatesSubscribed = false;
    private bool _disposed;
    private readonly SemaphoreSlim _connectionSemaphore = new(1, 1);
    private bool _alpacaReconnecting = false;
    private bool _polygonReconnecting = false;

    public StreamingDataService(
        IAlpacaClientFactory alpacaFactory,
        IPolygonClientFactory polygonFactory,
        ILogger<StreamingDataService> logger)
    {
        _alpacaFactory = alpacaFactory;
        _polygonFactory = polygonFactory;
        _logger = logger;
        ConnectionStatus = StreamingConnectionStatus.Disconnected;
    }

    // === Events ===
    
    public event EventHandler<StreamingQuoteEventArgs>? QuoteReceived;
    public event EventHandler<StreamingBarEventArgs>? BarReceived;
    public event EventHandler<IndexUpdateEventArgs>? IndexUpdated;
    public event EventHandler<TradeUpdateEventArgs>? TradeUpdated;

    // === Properties ===
    
    public StreamingConnectionStatus ConnectionStatus { get; private set; }

    // === Connection Management ===

    public async Task ConnectAlpacaStreamAsync()
    {
        await _connectionSemaphore.WaitAsync();
        try
        {
            if (ConnectionStatus == StreamingConnectionStatus.Connected &&
                _alpacaStreamingClient != null && _alpacaDataStreamingClient != null)
            {
                return; // Already connected
            }

            ConnectionStatus = StreamingConnectionStatus.Connecting;
            _logger.LogInformation("Connecting to Alpaca streaming services...");

            // Create streaming clients
            _alpacaStreamingClient = _alpacaFactory.CreateStreamingClient();
            _alpacaDataStreamingClient = _alpacaFactory.CreateDataStreamingClient();

            // Set up event handlers
            SetupAlpacaEventHandlers();

            // Connect and authenticate with retry logic
            await ConnectAlpacaWithRetryAsync();

            ConnectionStatus = StreamingConnectionStatus.Connected;
            _logger.LogInformation("Successfully connected to Alpaca streaming services");
        }
        catch (Exception ex)
        {
            ConnectionStatus = StreamingConnectionStatus.Error;
            _logger.LogError(ex, "Failed to connect to Alpaca streaming services");
            throw;
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    public async Task ConnectPolygonStreamAsync()
    {
        try
        {
            _logger.LogInformation("Connecting to Polygon streaming services...");

            // Create Polygon WebSocket client
            _polygonWebSocketClient = _polygonFactory.CreateWebSocketClient();

            // Set up event handlers
            SetupPolygonEventHandlers();

            // Connect and authenticate
            await _polygonWebSocketClient.ConnectAsync();

            _logger.LogInformation("Successfully connected to Polygon streaming services");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to Polygon streaming services");
            throw;
        }
    }

    public async Task DisconnectAllAsync()
    {
        try
        {
            _logger.LogInformation("Disconnecting from all streaming services...");

            // Disconnect Alpaca streaming clients
            if (_alpacaStreamingClient != null)
            {
                await _alpacaStreamingClient.DisconnectAsync();
                _alpacaStreamingClient.Dispose();
                _alpacaStreamingClient = null;
            }

            if (_alpacaDataStreamingClient != null)
            {
                await _alpacaDataStreamingClient.DisconnectAsync();
                _alpacaDataStreamingClient.Dispose();
                _alpacaDataStreamingClient = null;
            }

            // Disconnect Polygon WebSocket client
            if (_polygonWebSocketClient != null)
            {
                await _polygonWebSocketClient.DisconnectAsync();
                _polygonWebSocketClient.Dispose();
                _polygonWebSocketClient = null;
            }

            // Clear subscription tracking
            _subscribedQuoteSymbols.Clear();
            _subscribedBarSymbols.Clear();
            _subscribedIndexSymbols.Clear();

            ConnectionStatus = StreamingConnectionStatus.Disconnected;
            _logger.LogInformation("Disconnected from all streaming services");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during streaming disconnect");
        }
    }

    // === Subscription Management ===

    public async Task SubscribeToQuotesAsync(IEnumerable<string> symbols)
    {
        if (_alpacaDataStreamingClient == null)
        {
            throw new InvalidOperationException("Alpaca data streaming client is not connected");
        }

        var symbolList = symbols.ToList();
        _logger.LogInformation("Subscribing to quotes for {Count} symbols: {Symbols}",
            symbolList.Count, string.Join(", ", symbolList));

        // TODO: Implement actual Alpaca quote subscription when API is confirmed
        // await _alpacaDataStreamingClient.SubscribeAsync(symbolList.Select(s => new QuoteSubscription(s)));
        _logger.LogInformation("Quote subscription placeholder - API implementation pending");

        foreach (var symbol in symbolList)
        {
            _subscribedQuoteSymbols.Add(symbol);
        }
    }

    public async Task SubscribeToBarsAsync(IEnumerable<string> symbols)
    {
        if (_alpacaDataStreamingClient == null)
        {
            throw new InvalidOperationException("Alpaca data streaming client is not connected");
        }

        var symbolList = symbols.ToList();
        _logger.LogInformation("Subscribing to bars for {Count} symbols: {Symbols}",
            symbolList.Count, string.Join(", ", symbolList));

        // TODO: Implement actual Alpaca bar subscription when API is confirmed
        // await _alpacaDataStreamingClient.SubscribeAsync(symbolList.Select(s => new BarSubscription(s, BarTimeFrame.Minute)));
        _logger.LogInformation("Bar subscription placeholder - API implementation pending");

        foreach (var symbol in symbolList)
        {
            _subscribedBarSymbols.Add(symbol);
        }
    }

    public async Task SubscribeToIndexUpdatesAsync(IEnumerable<string> indexSymbols)
    {
        if (_polygonWebSocketClient == null)
        {
            throw new InvalidOperationException("Polygon WebSocket client is not connected");
        }

        var symbolList = indexSymbols.ToList();
        _logger.LogInformation("Subscribing to index updates for: {Symbols}", string.Join(", ", symbolList));

        await _polygonWebSocketClient.SubscribeToIndexUpdatesAsync(symbolList);

        foreach (var symbol in symbolList)
        {
            _subscribedIndexSymbols.Add(symbol);
        }
    }

    public async Task SubscribeToTradeUpdatesAsync()
    {
        if (_alpacaStreamingClient == null)
        {
            throw new InvalidOperationException("Alpaca streaming client is not connected");
        }

        _logger.LogInformation("Subscribing to trade updates");

        // TODO: Implement actual Alpaca trade update subscription when API is confirmed
        // await _alpacaStreamingClient.SubscribeAsync(new TradeUpdateSubscription());
        _logger.LogInformation("Trade update subscription placeholder - API implementation pending");
        _tradeUpdatesSubscribed = true;
    }

    public async Task UnsubscribeAllAsync()
    {
        try
        {
            _logger.LogInformation("Unsubscribing from all streaming data");

            // Unsubscribe from Alpaca data streams
            if (_alpacaDataStreamingClient != null)
            {
                if (_subscribedQuoteSymbols.Any())
                {
                    // TODO: Implement actual Alpaca quote unsubscription when API is confirmed
                    // await _alpacaDataStreamingClient.UnsubscribeAsync(_subscribedQuoteSymbols.Select(s => new QuoteSubscription(s)));
                    _logger.LogInformation("Quote unsubscription placeholder - API implementation pending");
                    _subscribedQuoteSymbols.Clear();
                }

                if (_subscribedBarSymbols.Any())
                {
                    // TODO: Implement actual Alpaca bar unsubscription when API is confirmed
                    // await _alpacaDataStreamingClient.UnsubscribeAsync(_subscribedBarSymbols.Select(s => new BarSubscription(s, BarTimeFrame.Minute)));
                    _logger.LogInformation("Bar unsubscription placeholder - API implementation pending");
                    _subscribedBarSymbols.Clear();
                }
            }

            // Unsubscribe from Alpaca trade updates
            if (_alpacaStreamingClient != null && _tradeUpdatesSubscribed)
            {
                // TODO: Implement actual Alpaca trade update unsubscription when API is confirmed
                // await _alpacaStreamingClient.UnsubscribeAsync(new TradeUpdateSubscription());
                _logger.LogInformation("Trade update unsubscription placeholder - API implementation pending");
                _tradeUpdatesSubscribed = false;
            }

            // Unsubscribe from Polygon index updates
            if (_polygonWebSocketClient != null && _subscribedIndexSymbols.Any())
            {
                await _polygonWebSocketClient.UnsubscribeAllAsync();
                _subscribedIndexSymbols.Clear();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unsubscribing from streaming data");
        }
    }

    // === Private Methods ===

    private async Task ConnectAlpacaWithRetryAsync()
    {
        const int maxRetries = 3;
        var baseDelay = TimeSpan.FromSeconds(1);

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                await _alpacaStreamingClient!.ConnectAndAuthenticateAsync();
                await _alpacaDataStreamingClient!.ConnectAndAuthenticateAsync();
                return; // Success
            }
            catch (Exception ex) when (attempt < maxRetries)
            {
                var delay = TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * Math.Pow(2, attempt - 1));
                _logger.LogWarning(ex, "Failed to connect to Alpaca (attempt {Attempt}/{MaxRetries}). Retrying in {Delay}ms",
                    attempt, maxRetries, delay.TotalMilliseconds);
                await Task.Delay(delay);
            }
        }
    }

    private void SetupAlpacaEventHandlers()
    {
        _logger.LogDebug("Setting up Alpaca event handlers");

        if (_alpacaDataStreamingClient != null)
        {
            // TODO: Set up actual Alpaca data streaming event handlers when API is confirmed
            // _alpacaDataStreamingClient.OnQuoteReceived += OnAlpacaQuoteReceived;
            // _alpacaDataStreamingClient.OnBarReceived += OnAlpacaBarReceived;
            // _alpacaDataStreamingClient.SocketClosed += OnAlpacaDataSocketClosed;
            _logger.LogDebug("Alpaca data streaming event handlers placeholder - API implementation pending");
        }

        if (_alpacaStreamingClient != null)
        {
            // TODO: Set up actual Alpaca streaming event handlers when API is confirmed
            // _alpacaStreamingClient.OnTradeUpdateReceived += OnAlpacaTradeUpdateReceived;
            // _alpacaStreamingClient.SocketClosed += OnAlpacaStreamingSocketClosed;
            _logger.LogDebug("Alpaca streaming event handlers placeholder - API implementation pending");
        }
    }

    private void SetupPolygonEventHandlers()
    {
        _logger.LogDebug("Setting up Polygon event handlers");

        if (_polygonWebSocketClient != null)
        {
            _polygonWebSocketClient.IndexUpdated += OnPolygonIndexUpdated;
            _polygonWebSocketClient.ErrorOccurred += OnPolygonErrorOccurred;
            _polygonWebSocketClient.ConnectionStatusChanged += OnPolygonConnectionStatusChanged;
        }
    }

    private void OnAlpacaQuoteReceived(IQuote quote)
    {
        try
        {
            var eventArgs = new StreamingQuoteEventArgs
            {
                Symbol = quote.Symbol,
                BidPrice = quote.BidPrice,
                AskPrice = quote.AskPrice,
                BidSize = (long)quote.BidSize,
                AskSize = (long)quote.AskSize,
                Timestamp = quote.TimestampUtc
            };

            QuoteReceived?.Invoke(this, eventArgs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing quote for {Symbol}", quote.Symbol);
        }
    }

    private void OnAlpacaBarReceived(IBar bar)
    {
        try
        {
            var eventArgs = new StreamingBarEventArgs
            {
                Symbol = bar.Symbol,
                Open = bar.Open,
                High = bar.High,
                Low = bar.Low,
                Close = bar.Close,
                Volume = (long)bar.Volume,
                Timestamp = bar.TimeUtc
            };

            BarReceived?.Invoke(this, eventArgs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing bar for {Symbol}", bar.Symbol);
        }
    }

    private void OnAlpacaTradeUpdateReceived(ITradeUpdate tradeUpdate)
    {
        try
        {
            var eventArgs = new TradeUpdateEventArgs
            {
                Symbol = tradeUpdate.Order.Symbol,
                OrderId = tradeUpdate.Order.OrderId.ToString(),
                Quantity = tradeUpdate.Order.Quantity ?? 0,
                Price = tradeUpdate.Price ?? 0,
                Side = tradeUpdate.Order.OrderSide.ToString().ToLowerInvariant(),
                Status = tradeUpdate.Event.ToString().ToLowerInvariant(),
                Timestamp = tradeUpdate.TimestampUtc ?? DateTime.UtcNow
            };

            TradeUpdated?.Invoke(this, eventArgs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trade update for order {OrderId}", tradeUpdate.Order.OrderId);
        }
    }

    private void OnPolygonIndexUpdated(object? sender, PolygonIndexUpdateEventArgs e)
    {
        try
        {
            var eventArgs = new IndexUpdateEventArgs
            {
                IndexSymbol = e.IndexSymbol,
                Value = e.Value,
                Change = e.Change,
                ChangePercent = e.ChangePercent,
                Timestamp = e.Timestamp
            };

            IndexUpdated?.Invoke(this, eventArgs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing index update for {Symbol}", e.IndexSymbol);
        }
    }

    private void OnPolygonErrorOccurred(object? sender, PolygonErrorEventArgs e)
    {
        _logger.LogError(e.Exception, "Polygon WebSocket error: {Message}", e.Message);
    }

    private void OnPolygonConnectionStatusChanged(object? sender, PolygonConnectionStatusEventArgs e)
    {
        _logger.LogInformation("Polygon connection status changed to {Status}: {Message}", e.Status, e.Message);

        if (e.Status == PolygonConnectionStatus.Error && e.Exception != null)
        {
            _logger.LogError(e.Exception, "Polygon connection error");
        }
    }

    private async void OnAlpacaDataSocketClosed(object? sender, EventArgs e)
    {
        _logger.LogWarning("Alpaca data streaming socket closed unexpectedly");
        await HandleAlpacaReconnectionAsync();
    }

    private async void OnAlpacaStreamingSocketClosed(object? sender, EventArgs e)
    {
        _logger.LogWarning("Alpaca streaming socket closed unexpectedly");
        await HandleAlpacaReconnectionAsync();
    }

    private async Task HandleAlpacaReconnectionAsync()
    {
        if (_alpacaReconnecting || _disposed)
        {
            return;
        }

        _alpacaReconnecting = true;
        ConnectionStatus = StreamingConnectionStatus.Reconnecting;

        try
        {
            _logger.LogInformation("Attempting to reconnect Alpaca streaming services...");

            // Store current subscriptions for re-subscription
            var quoteSymbols = _subscribedQuoteSymbols.ToList();
            var barSymbols = _subscribedBarSymbols.ToList();
            var tradeUpdatesWereSubscribed = _tradeUpdatesSubscribed;

            // Dispose current clients
            _alpacaStreamingClient?.Dispose();
            _alpacaDataStreamingClient?.Dispose();

            // Reconnect
            await ConnectAlpacaStreamAsync();

            // Re-subscribe to previous subscriptions
            if (quoteSymbols.Any())
            {
                await SubscribeToQuotesAsync(quoteSymbols);
            }

            if (barSymbols.Any())
            {
                await SubscribeToBarsAsync(barSymbols);
            }

            if (tradeUpdatesWereSubscribed)
            {
                await SubscribeToTradeUpdatesAsync();
            }

            _logger.LogInformation("Successfully reconnected Alpaca streaming services");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reconnect Alpaca streaming services");
            ConnectionStatus = StreamingConnectionStatus.Error;
        }
        finally
        {
            _alpacaReconnecting = false;
        }
    }

    // === IDisposable ===

    public void Dispose()
    {
        if (!_disposed)
        {
            try
            {
                // Dispose clients synchronously to avoid deadlocks
                _alpacaStreamingClient?.Dispose();
                _alpacaDataStreamingClient?.Dispose();
                _polygonWebSocketClient?.Dispose();

                // Clear subscription tracking
                _subscribedQuoteSymbols.Clear();
                _subscribedBarSymbols.Clear();
                _subscribedIndexSymbols.Clear();

                ConnectionStatus = StreamingConnectionStatus.Disconnected;
                _logger.LogInformation("Disposed streaming data service");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during streaming service disposal");
            }
            finally
            {
                _connectionSemaphore?.Dispose();
                _disposed = true;
            }
        }
    }
}
