using SmaTrendFollower.Services;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Console.Examples;

/// <summary>
/// Enhanced example demonstrating the full capabilities of the unified market data system
/// combining Alpaca (account, positions, live fills, equity data) and Polygon (indices, options)
/// </summary>
public class EnhancedMarketDataExample
{
    private readonly IMarketDataService _marketDataService;
    private readonly IStreamingDataService _streamingService;
    private readonly ILogger<EnhancedMarketDataExample> _logger;

    public EnhancedMarketDataExample(
        IMarketDataService marketDataService,
        IStreamingDataService streamingService,
        ILogger<EnhancedMarketDataExample> logger)
    {
        _marketDataService = marketDataService;
        _streamingService = streamingService;
        _logger = logger;
    }

    /// <summary>
    /// Demonstrates the complete enhanced market data capabilities
    /// </summary>
    public async Task RunEnhancedExampleAsync()
    {
        _logger.LogInformation("=== Enhanced Market Data Service Example ===");

        try
        {
            // 1. Account & Portfolio Data
            await DemonstrateAccountDataAsync();

            // 2. Historical Data (Daily & Minute)
            await DemonstrateHistoricalDataAsync();

            // 3. Index & Volatility Data
            await DemonstrateIndexDataAsync();

            // 4. Options Data
            await DemonstrateOptionsDataAsync();

            // 5. Real-time Streaming (if enabled)
            await DemonstrateStreamingDataAsync();

            _logger.LogInformation("=== Enhanced example completed successfully ===");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in enhanced market data example");
        }
    }

    private async Task DemonstrateAccountDataAsync()
    {
        _logger.LogInformation("--- Account & Portfolio Data ---");

        try
        {
            // Get account information
            var account = await _marketDataService.GetAccountAsync();
            _logger.LogInformation("Account Equity: {Equity:C}, Buying Power: {BuyingPower:C}, " +
                                 "Day Trading Buying Power: {DayTradingBuyingPower:C}",
                account.Equity, account.BuyingPower, account.DayTradingBuyingPower);

            // Get current positions
            var positions = await _marketDataService.GetPositionsAsync();
            _logger.LogInformation("Current Positions: {Count}", positions.Count);
            
            foreach (var position in positions.Take(5)) // Show first 5 positions
            {
                _logger.LogInformation("Position: {Symbol} - Qty: {Qty}, Market Value: {MarketValue:C}, " +
                                     "Unrealized P&L: {UnrealizedPL:C}",
                    position.Symbol, position.Quantity, position.MarketValue, position.UnrealizedProfitLoss);
            }

            // Get recent fills
            var recentFills = await _marketDataService.GetRecentFillsAsync(10);
            _logger.LogInformation("Recent Fills: {Count}", recentFills.Count);
            
            foreach (var fill in recentFills.Take(3)) // Show last 3 fills
            {
                _logger.LogInformation("Fill: {Symbol} - {Side} {Qty} @ {Price:C} on {Date}",
                    fill.Symbol, fill.OrderSide, fill.Quantity, fill.AverageFillPrice, 
                    fill.FilledAtUtc?.ToString("yyyy-MM-dd HH:mm"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving account data");
        }
    }

    private async Task DemonstrateHistoricalDataAsync()
    {
        _logger.LogInformation("--- Historical Data (Daily & Minute) ---");

        try
        {
            var symbols = new[] { "SPY", "QQQ", "AAPL" };
            var startDate = DateTime.UtcNow.AddDays(-7);
            var endDate = DateTime.UtcNow;

            // Daily bars
            var dailyBars = await _marketDataService.GetStockBarsAsync(symbols, startDate, endDate);
            _logger.LogInformation("Retrieved daily bars for {Count} symbols", dailyBars.Count);

            // Minute bars (with fallback to Polygon if Alpaca throttles)
            var minuteBars = await _marketDataService.GetStockMinuteBarsAsync(symbols, startDate, endDate);
            _logger.LogInformation("Retrieved minute bars for {Count} symbols", minuteBars.Count);

            foreach (var kvp in dailyBars.Take(2))
            {
                var latestBar = kvp.Value.Items.LastOrDefault();
                if (latestBar != null)
                {
                    _logger.LogInformation("{Symbol} latest: {Close:C} (Volume: {Volume:N0})",
                        kvp.Key, latestBar.Close, latestBar.Volume);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving historical data");
        }
    }

    private async Task DemonstrateIndexDataAsync()
    {
        _logger.LogInformation("--- Index & Volatility Data ---");

        try
        {
            // Current index values
            var spxValue = await _marketDataService.GetIndexValueAsync("I:SPX");
            var vixValue = await _marketDataService.GetIndexValueAsync("I:VIX");
            
            _logger.LogInformation("S&P 500 (SPX): {Value:F2}", spxValue ?? 0);
            _logger.LogInformation("VIX: {Value:F2}", vixValue ?? 0);

            // Historical index data
            var startDate = DateTime.UtcNow.AddDays(-30);
            var endDate = DateTime.UtcNow;
            
            var vixHistory = await _marketDataService.GetIndexBarsAsync("I:VIX", startDate, endDate);
            var vixBars = vixHistory.ToList();
            
            if (vixBars.Any())
            {
                var avgVix = vixBars.Average(b => b.Close);
                var maxVix = vixBars.Max(b => b.High);
                _logger.LogInformation("VIX 30-day stats: Avg={Avg:F2}, Max={Max:F2} ({Count} bars)",
                    avgVix, maxVix, vixBars.Count);
            }

            // VIX term structure
            var vixTerm = await _marketDataService.GetVixTermStructureAsync();
            var termPoints = vixTerm.ToList();
            _logger.LogInformation("VIX term structure points: {Count}", termPoints.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving index data");
        }
    }

    private async Task DemonstrateOptionsDataAsync()
    {
        _logger.LogInformation("--- Options Data (Greeks, IV, OI) ---");

        try
        {
            // Get options data for SPY
            var spyOptions = await _marketDataService.GetOptionsDataAsync("SPY");
            var optionsList = spyOptions.ToList();
            
            _logger.LogInformation("SPY options contracts: {Count}", optionsList.Count);

            // Show some call options
            var calls = optionsList.Where(o => o.OptionType.ToLower() == "call").Take(5);
            foreach (var call in calls)
            {
                _logger.LogInformation("Call: {Symbol} Strike={Strike:C} Exp={Exp:yyyy-MM-dd} " +
                                     "IV={IV:P2} Delta={Delta:F3}",
                    call.Symbol, call.Strike, call.ExpirationDate, 
                    call.ImpliedVolatility ?? 0, call.Delta ?? 0);
            }

            // Get options for a specific expiration
            var nextFriday = GetNextFriday();
            var weeklyOptions = await _marketDataService.GetOptionsDataAsync("SPY", nextFriday);
            _logger.LogInformation("SPY weekly options for {Date:yyyy-MM-dd}: {Count}",
                nextFriday, weeklyOptions.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving options data");
        }
    }

    private async Task DemonstrateStreamingDataAsync()
    {
        _logger.LogInformation("--- Real-time Streaming Data ---");

        try
        {
            // Set up event handlers
            _streamingService.QuoteReceived += OnQuoteReceived;
            _streamingService.BarReceived += OnBarReceived;
            _streamingService.TradeUpdated += OnTradeUpdated;
            _streamingService.IndexUpdated += OnIndexUpdated;

            // Connect to streaming services
            await _streamingService.ConnectAlpacaStreamAsync();
            
            // Subscribe to some symbols
            var symbols = new[] { "SPY", "QQQ", "AAPL" };
            await _streamingService.SubscribeToQuotesAsync(symbols);
            await _streamingService.SubscribeToBarsAsync(symbols);
            await _streamingService.SubscribeToTradeUpdatesAsync();

            // Subscribe to index updates for volatility monitoring
            var indices = new[] { "I:VIX", "I:SPX" };
            await _streamingService.SubscribeToIndexUpdatesAsync(indices);

            _logger.LogInformation("Streaming subscriptions active. Status: {Status}", 
                _streamingService.ConnectionStatus);

            // Let it run for a short time in this example
            await Task.Delay(5000);

            // Clean up
            await _streamingService.UnsubscribeAllAsync();
            await _streamingService.DisconnectAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error with streaming data");
        }
    }

    private void OnQuoteReceived(object? sender, StreamingQuoteEventArgs e)
    {
        _logger.LogDebug("Quote: {Symbol} Bid={Bid:C} Ask={Ask:C}", e.Symbol, e.BidPrice, e.AskPrice);
    }

    private void OnBarReceived(object? sender, StreamingBarEventArgs e)
    {
        _logger.LogDebug("Bar: {Symbol} Close={Close:C} Volume={Volume:N0}", e.Symbol, e.Close, e.Volume);
    }

    private void OnTradeUpdated(object? sender, TradeUpdateEventArgs e)
    {
        _logger.LogInformation("Trade Update: {Symbol} {Side} {Qty} @ {Price:C} Status={Status}",
            e.Symbol, e.Side, e.Quantity, e.Price, e.Status);
    }

    private void OnIndexUpdated(object? sender, IndexUpdateEventArgs e)
    {
        _logger.LogInformation("Index Update: {Index} = {Value:F2} ({Change:+F2})",
            e.IndexSymbol, e.Value, e.Change);
    }

    private static DateTime GetNextFriday()
    {
        var today = DateTime.Today;
        var daysUntilFriday = ((int)DayOfWeek.Friday - (int)today.DayOfWeek + 7) % 7;
        if (daysUntilFriday == 0 && today.DayOfWeek == DayOfWeek.Friday)
            daysUntilFriday = 7; // Next Friday if today is Friday
        return today.AddDays(daysUntilFriday);
    }
}
