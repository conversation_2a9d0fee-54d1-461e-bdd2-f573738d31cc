namespace SmaTrendFollower.Services;

/// <summary>
/// Service for advanced cache management operations and monitoring
/// </summary>
public interface ICacheManagementService
{
    /// <summary>
    /// Gets comprehensive cache dashboard data
    /// </summary>
    /// <returns>Dashboard data with all cache statistics</returns>
    Task<CacheDashboardData> GetDashboardDataAsync();

    /// <summary>
    /// Manually refreshes cache for specific symbols
    /// </summary>
    /// <param name="symbols">Symbols to refresh</param>
    /// <param name="timeFrames">Timeframes to refresh (default: Day and Minute)</param>
    /// <param name="daysOfHistory">Days of history to refresh (default: 30)</param>
    /// <returns>Refresh operation results</returns>
    Task<CacheRefreshResult> RefreshSymbolsAsync(IEnumerable<string> symbols, IEnumerable<string>? timeFrames = null, int daysOfHistory = 30);

    /// <summary>
    /// Performs comprehensive cache maintenance
    /// </summary>
    /// <param name="options">Maintenance options</param>
    /// <returns>Maintenance operation results</returns>
    Task<CacheMaintenanceResult> PerformMaintenanceAsync(CacheMaintenanceOptions options);

    /// <summary>
    /// Validates cache integrity and reports issues
    /// </summary>
    /// <returns>Validation results</returns>
    Task<CacheValidationResult> ValidateCacheIntegrityAsync();

    /// <summary>
    /// Exports cache data for backup or analysis
    /// </summary>
    /// <param name="exportOptions">Export configuration</param>
    /// <returns>Export operation results</returns>
    Task<CacheExportResult> ExportCacheDataAsync(CacheExportOptions exportOptions);

    /// <summary>
    /// Imports cache data from backup
    /// </summary>
    /// <param name="importOptions">Import configuration</param>
    /// <returns>Import operation results</returns>
    Task<CacheImportResult> ImportCacheDataAsync(CacheImportOptions importOptions);

    /// <summary>
    /// Gets cache health status
    /// </summary>
    /// <returns>Overall cache health assessment</returns>
    Task<CacheHealthStatus> GetCacheHealthAsync();

    /// <summary>
    /// Optimizes cache performance
    /// </summary>
    /// <returns>Optimization results</returns>
    Task<CacheOptimizationResult> OptimizeCacheAsync();
}

/// <summary>
/// Comprehensive cache dashboard data
/// </summary>
public readonly record struct CacheDashboardData(
    CacheOverviewStats Overview,
    IDictionary<string, SymbolCacheInfo> TopSymbols,
    CachePerformanceMetrics Performance,
    CompressionStats Compression,
    CacheHealthStatus Health,
    CacheWarmingStats Warming,
    DateTime LastUpdated
);

/// <summary>
/// Cache overview statistics
/// </summary>
public readonly record struct CacheOverviewStats(
    int TotalSymbols,
    long TotalBars,
    long TotalSizeBytes,
    long CompressedSizeBytes,
    double CompressionRatio,
    DateTime OldestData,
    DateTime NewestData,
    TimeSpan DataSpan
);

/// <summary>
/// Information about a cached symbol
/// </summary>
public readonly record struct SymbolCacheInfo(
    string Symbol,
    IDictionary<string, TimeFrameInfo> TimeFrames,
    long TotalBars,
    long SizeBytes,
    DateTime LastUpdated,
    DateTime OldestBar,
    DateTime NewestBar,
    double CacheHitRatio
);

/// <summary>
/// Information about a timeframe for a symbol
/// </summary>
public readonly record struct TimeFrameInfo(
    string TimeFrame,
    int BarCount,
    DateTime OldestBar,
    DateTime NewestBar,
    bool IsCompressed,
    long SizeBytes
);

/// <summary>
/// Cache refresh operation result
/// </summary>
public readonly record struct CacheRefreshResult(
    int SymbolsProcessed,
    int SymbolsSuccessful,
    int SymbolsFailed,
    long NewBarsAdded,
    long BarsUpdated,
    TimeSpan Duration,
    string[] FailedSymbols,
    string[] Errors
);

/// <summary>
/// Cache maintenance options
/// </summary>
public readonly record struct CacheMaintenanceOptions(
    bool CleanupOldData,
    int RetentionDays,
    bool CompressOldBars,
    int CompressionAgeDays,
    bool OptimizeDatabase,
    bool ValidateIntegrity,
    bool UpdateStatistics
);

/// <summary>
/// Cache maintenance operation result
/// </summary>
public readonly record struct CacheMaintenanceResult(
    bool Success,
    TimeSpan Duration,
    long DataCleaned,
    int BarsCompressed,
    long SpaceSaved,
    int IntegrityIssuesFixed,
    string[] Operations,
    string[] Errors
);

/// <summary>
/// Cache validation result
/// </summary>
public readonly record struct CacheValidationResult(
    bool IsValid,
    int TotalChecks,
    int IssuesFound,
    ValidationIssue[] Issues,
    string[] Recommendations
);

/// <summary>
/// Cache validation issue
/// </summary>
public readonly record struct ValidationIssue(
    string Severity, // "Error", "Warning", "Info"
    string Category, // "Data", "Integrity", "Performance"
    string Description,
    string Symbol,
    string TimeFrame,
    DateTime? AffectedDate,
    string RecommendedAction
);

/// <summary>
/// Cache export options
/// </summary>
public readonly record struct CacheExportOptions(
    string ExportPath,
    string[] Symbols,
    string[] TimeFrames,
    DateTime? StartDate,
    DateTime? EndDate,
    bool IncludeMetadata,
    bool CompressExport
);

/// <summary>
/// Cache export result
/// </summary>
public readonly record struct CacheExportResult(
    bool Success,
    string ExportPath,
    long ExportedBars,
    long ExportSizeBytes,
    TimeSpan Duration,
    string[] Errors
);

/// <summary>
/// Cache import options
/// </summary>
public readonly record struct CacheImportOptions(
    string ImportPath,
    bool OverwriteExisting,
    bool ValidateData,
    bool UpdateMetadata
);

/// <summary>
/// Cache import result
/// </summary>
public readonly record struct CacheImportResult(
    bool Success,
    long ImportedBars,
    int ImportedSymbols,
    long SkippedBars,
    TimeSpan Duration,
    string[] Errors
);

/// <summary>
/// Cache health status
/// </summary>
public readonly record struct CacheHealthStatus(
    string OverallHealth, // "Excellent", "Good", "Fair", "Poor"
    double HealthScore, // 0.0 to 1.0
    HealthMetric[] Metrics,
    string[] Recommendations
);

/// <summary>
/// Individual health metric
/// </summary>
public readonly record struct HealthMetric(
    string Name,
    double Value,
    double Threshold,
    string Status, // "Good", "Warning", "Critical"
    string Description
);

/// <summary>
/// Cache optimization result
/// </summary>
public readonly record struct CacheOptimizationResult(
    bool Success,
    TimeSpan Duration,
    long SpaceSaved,
    double PerformanceImprovement,
    string[] OptimizationsApplied,
    string[] Errors
);

/// <summary>
/// Default cache maintenance options
/// </summary>
public static class CacheMaintenanceDefaults
{
    public static CacheMaintenanceOptions Standard => new(
        CleanupOldData: true,
        RetentionDays: 365,
        CompressOldBars: true,
        CompressionAgeDays: 30,
        OptimizeDatabase: true,
        ValidateIntegrity: false,
        UpdateStatistics: true
    );

    public static CacheMaintenanceOptions Quick => new(
        CleanupOldData: false,
        RetentionDays: 365,
        CompressOldBars: false,
        CompressionAgeDays: 30,
        OptimizeDatabase: true,
        ValidateIntegrity: false,
        UpdateStatistics: true
    );

    public static CacheMaintenanceOptions Full => new(
        CleanupOldData: true,
        RetentionDays: 365,
        CompressOldBars: true,
        CompressionAgeDays: 30,
        OptimizeDatabase: true,
        ValidateIntegrity: true,
        UpdateStatistics: true
    );
}
