using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

public sealed class PolygonClientFactory : IPolygonClientFactory
{
    private readonly HttpClient _client;
    private readonly PolygonRateLimitHelper _rateLimitHelper;
    private readonly IConfiguration _configuration;
    private readonly ILogger<PolygonWebSocketClient> _webSocketLogger;
    private readonly string? _apiKey;

    public PolygonClientFactory(
        IHttpClientFactory httpFactory,
        IConfiguration config,
        ILogger<PolygonRateLimitHelper> rateLimitLogger,
        ILogger<PolygonWebSocketClient> webSocketLogger)
    {
        _client = httpFactory.CreateClient("polygon");
        _client.BaseAddress = new Uri("https://api.polygon.io/");
        _configuration = config;
        _webSocketLogger = webSocketLogger;

        // Store API key for query parameter injection
        _apiKey = config["POLY_API_KEY"];

        _rateLimitHelper = new PolygonRateLimitHelper(rateLimitLogger);
    }

    public HttpClient CreateClient() => _client;

    public IPolygonWebSocketClient CreateWebSocketClient()
    {
        return new PolygonWebSocketClient(_configuration, _webSocketLogger);
    }

    public IPolygonRateLimitHelper GetRateLimitHelper() => _rateLimitHelper;

    /// <summary>
    /// Adds the API key as a query parameter to the given URL
    /// </summary>
    /// <param name="url">The base URL without API key</param>
    /// <returns>URL with apiKey query parameter appended</returns>
    public string AddApiKeyToUrl(string url)
    {
        if (string.IsNullOrEmpty(_apiKey))
        {
            return url;
        }

        var separator = url.Contains('?') ? "&" : "?";
        return $"{url}{separator}apiKey={_apiKey}";
    }

    public void Dispose()
    {
        _rateLimitHelper?.Dispose();
        _client?.Dispose();
    }
}
