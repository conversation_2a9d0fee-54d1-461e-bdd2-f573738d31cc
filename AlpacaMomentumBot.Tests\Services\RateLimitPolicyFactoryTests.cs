using SmaTrendFollower.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Polly;
using System.Net;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class RateLimitPolicyFactoryTests
{
    private readonly Mock<ILogger<RateLimitPolicyFactory>> _mockLogger;
    private readonly RateLimitPolicyFactory _factory;

    public RateLimitPolicyFactoryTests()
    {
        _mockLogger = new Mock<ILogger<RateLimitPolicyFactory>>();
        _factory = new RateLimitPolicyFactory(_mockLogger.Object);
    }

    [Fact]
    public void CreateAlpacaPolicy_ShouldReturnValidPolicy()
    {
        // Act
        var policy = _factory.CreateAlpacaPolicy();

        // Assert
        policy.Should().NotBeNull();
        policy.Should().BeAssignableTo<IAsyncPolicy<HttpResponseMessage>>();
    }

    [Fact]
    public void CreatePolygonPolicy_ShouldReturnValidPolicy()
    {
        // Act
        var policy = _factory.CreatePolygonPolicy();

        // Assert
        policy.Should().NotBeNull();
        policy.Should().BeAssignableTo<IAsyncPolicy<HttpResponseMessage>>();
    }

    [Fact]
    public async Task AlpacaPolicy_ShouldRetryOnTooManyRequests()
    {
        // Arrange
        var policy = _factory.CreateAlpacaPolicy();
        var callCount = 0;

        // Act & Assert
        var exception = await Assert.ThrowsAsync<HttpRequestException>(async () =>
        {
            await policy.ExecuteAsync(async () =>
            {
                callCount++;
                if (callCount <= 3)
                {
                    var response = new HttpResponseMessage(HttpStatusCode.TooManyRequests);
                    return response;
                }
                throw new HttpRequestException("Final failure");
            });
        });

        // Should have retried multiple times
        callCount.Should().BeGreaterThan(1);
    }

    [Fact]
    public async Task PolygonPolicy_ShouldRetryOnTooManyRequests()
    {
        // Arrange
        var policy = _factory.CreatePolygonPolicy();
        var callCount = 0;

        // Act & Assert
        var exception = await Assert.ThrowsAsync<HttpRequestException>(async () =>
        {
            await policy.ExecuteAsync(async () =>
            {
                callCount++;
                if (callCount <= 3)
                {
                    var response = new HttpResponseMessage(HttpStatusCode.TooManyRequests);
                    return response;
                }
                throw new HttpRequestException("Final failure");
            });
        });

        // Should have retried multiple times
        callCount.Should().BeGreaterThan(1);
    }

    [Fact]
    public async Task AlpacaPolicy_ShouldSucceedOnValidResponse()
    {
        // Arrange
        var policy = _factory.CreateAlpacaPolicy();
        var expectedResponse = new HttpResponseMessage(HttpStatusCode.OK);

        // Act
        var result = await policy.ExecuteAsync(async () =>
        {
            await Task.Delay(10); // Simulate async work
            return expectedResponse;
        });

        // Assert
        result.Should().Be(expectedResponse);
        result.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task PolygonPolicy_ShouldSucceedOnValidResponse()
    {
        // Arrange
        var policy = _factory.CreatePolygonPolicy();
        var expectedResponse = new HttpResponseMessage(HttpStatusCode.OK);

        // Act
        var result = await policy.ExecuteAsync(async () =>
        {
            await Task.Delay(10); // Simulate async work
            return expectedResponse;
        });

        // Assert
        result.Should().Be(expectedResponse);
        result.StatusCode.Should().Be(HttpStatusCode.OK);
    }
}
