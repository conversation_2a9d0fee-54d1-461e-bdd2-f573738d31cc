using SmaTrendFollower.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class AlpacaRateLimitHelperTests
{
    private readonly Mock<ILogger<AlpacaRateLimitHelper>> _mockLogger;
    private readonly AlpacaRateLimitHelper _helper;

    public AlpacaRateLimitHelperTests()
    {
        _mockLogger = new Mock<ILogger<AlpacaRateLimitHelper>>();
        _helper = new AlpacaRateLimitHelper(_mockLogger.Object);
    }

    [Fact]
    public async Task ExecuteAsync_ShouldExecuteSuccessfulOperation()
    {
        // Arrange
        var expectedResult = "success";

        // Act
        var result = await _helper.ExecuteAsync(async () =>
        {
            await Task.Delay(10);
            return expectedResult;
        }, "TestOperation");

        // Assert
        result.Should().Be(expectedResult);
    }

    [Fact]
    public async Task ExecuteAsync_ShouldRetryOnRateLimitException()
    {
        // Arrange
        var callCount = 0;
        var expectedResult = "success";

        // Act
        var result = await _helper.ExecuteAsync(async () =>
        {
            callCount++;
            if (callCount == 1)
            {
                throw new Exception("TooManyRequests");
            }
            return expectedResult;
        }, "TestOperation");

        // Assert
        result.Should().Be(expectedResult);
        callCount.Should().Be(2);
    }

    [Fact]
    public async Task ExecuteAsync_WithoutReturnValue_ShouldExecuteSuccessfully()
    {
        // Arrange
        var executed = false;

        // Act
        await _helper.ExecuteAsync(async () =>
        {
            await Task.Delay(10);
            executed = true;
        }, "TestOperation");

        // Assert
        executed.Should().BeTrue();
    }

    [Fact]
    public async Task ExecuteAsync_ShouldHandleConcurrentRequests()
    {
        // Arrange
        var tasks = new List<Task<string>>();
        
        // Act
        for (int i = 0; i < 10; i++)
        {
            var taskIndex = i;
            tasks.Add(_helper.ExecuteAsync(async () =>
            {
                await Task.Delay(50);
                return $"result-{taskIndex}";
            }, $"ConcurrentOperation-{taskIndex}"));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(10);
        results.Should().OnlyContain(r => r.StartsWith("result-"));
    }

    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _helper.Dispose();
        action.Should().NotThrow();
    }
}
