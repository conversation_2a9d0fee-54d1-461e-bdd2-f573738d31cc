using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Data;

/// <summary>
/// Entity Framework DbContext for SQLite stock bar caching.
/// Manages cached stock/ETF data to reduce API calls to Alpaca/Polygon.
/// Implements 1-year retention with efficient indexing.
/// </summary>
public class StockBarCacheDbContext : DbContext
{
    private readonly ILogger<StockBarCacheDbContext>? _logger;

    public StockBarCacheDbContext(DbContextOptions<StockBarCacheDbContext> options) : base(options)
    {
    }

    public StockBarCacheDbContext(DbContextOptions<StockBarCacheDbContext> options, ILogger<StockBarCacheDbContext> logger) : base(options)
    {
        _logger = logger;
    }

    /// <summary>
    /// Cached stock/ETF bars from Alpaca/Polygon APIs
    /// </summary>
    public DbSet<CachedStockBar> CachedStockBars { get; set; } = null!;

    /// <summary>
    /// Metadata about cached data for each symbol and timeframe
    /// </summary>
    public DbSet<StockCacheMetadata> StockCacheMetadata { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure CachedStockBar
        modelBuilder.Entity<CachedStockBar>(entity =>
        {
            // Composite unique index for efficient queries and deduplication
            entity.HasIndex(e => new { e.Symbol, e.TimeFrame, e.TimeUtc })
                  .IsUnique()
                  .HasDatabaseName("IX_CachedStockBars_Symbol_TimeFrame_TimeUtc");

            // Additional indexes for common query patterns
            entity.HasIndex(e => new { e.Symbol, e.TimeFrame })
                  .HasDatabaseName("IX_CachedStockBars_Symbol_TimeFrame");

            entity.HasIndex(e => e.TimeUtc)
                  .HasDatabaseName("IX_CachedStockBars_TimeUtc");

            entity.HasIndex(e => e.CachedAt)
                  .HasDatabaseName("IX_CachedStockBars_CachedAt");
        });

        // Configure StockCacheMetadata
        modelBuilder.Entity<StockCacheMetadata>(entity =>
        {
            entity.HasKey(e => e.CacheKey);
            
            entity.HasIndex(e => new { e.Symbol, e.TimeFrame })
                  .HasDatabaseName("IX_StockCacheMetadata_Symbol_TimeFrame");
        });
    }

    /// <summary>
    /// Ensures the database is created and migrations are applied with optimization
    /// </summary>
    public async Task EnsureDatabaseCreatedAsync()
    {
        var created = await Database.EnsureCreatedAsync();
        if (created)
        {
            _logger?.LogInformation("Database created successfully");

            // Apply initial optimizations for new database
            await ApplyInitialOptimizationsAsync();
        }
    }

    /// <summary>
    /// Applies initial database optimizations for new databases
    /// </summary>
    private async Task ApplyInitialOptimizationsAsync()
    {
        try
        {
            var optimizationCommands = new[]
            {
                "PRAGMA journal_mode=WAL;",
                "PRAGMA synchronous=NORMAL;",
                "PRAGMA cache_size=10000;",
                "PRAGMA temp_store=MEMORY;",
                "PRAGMA mmap_size=268435456;",
                "PRAGMA optimize;"
            };

            foreach (var command in optimizationCommands)
            {
                await Database.ExecuteSqlRawAsync(command);
            }

            _logger?.LogDebug("Applied initial database optimizations");
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Failed to apply some database optimizations");
        }
    }

    /// <summary>
    /// Gets cached bars for a symbol and timeframe within a date range
    /// </summary>
    public async Task<List<CachedStockBar>> GetCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        return await CachedStockBars
            .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame && b.TimeUtc >= startDate && b.TimeUtc <= endDate)
            .OrderBy(b => b.TimeUtc)
            .ToListAsync();
    }

    /// <summary>
    /// Gets the latest cached date for a symbol and timeframe
    /// </summary>
    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame)
    {
        var cacheKey = StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var metadata = await StockCacheMetadata.FindAsync(cacheKey);
        return metadata?.LatestDataDate;
    }

    /// <summary>
    /// Gets the earliest cached date for a symbol and timeframe
    /// </summary>
    public async Task<DateTime?> GetEarliestCachedDateAsync(string symbol, string timeFrame)
    {
        var cacheKey = StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var metadata = await StockCacheMetadata.FindAsync(cacheKey);
        return metadata?.EarliestDataDate;
    }

    /// <summary>
    /// Adds or updates cached bars for a symbol and timeframe using optimized bulk operations
    /// </summary>
    public async Task AddOrUpdateCachedBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars)
    {
        var cachedBars = bars.Select(bar => CachedStockBar.FromIBar(symbol, timeFrame, bar)).ToList();

        if (!cachedBars.Any())
            return;

        using var transaction = await Database.BeginTransactionAsync();
        try
        {
            // Bulk check for existing bars to minimize database round trips
            var timeStamps = cachedBars.Select(b => b.TimeUtc).ToList();
            var existingBars = await CachedStockBars
                .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame && timeStamps.Contains(b.TimeUtc))
                .ToDictionaryAsync(b => b.TimeUtc, b => b);

            var newBars = new List<CachedStockBar>();
            var updatedBars = new List<CachedStockBar>();

            foreach (var bar in cachedBars)
            {
                if (existingBars.TryGetValue(bar.TimeUtc, out var existing))
                {
                    // Update existing bar with latest data
                    existing.Open = bar.Open;
                    existing.High = bar.High;
                    existing.Low = bar.Low;
                    existing.Close = bar.Close;
                    existing.Volume = bar.Volume;
                    existing.Vwap = bar.Vwap;
                    existing.TradeCount = bar.TradeCount;
                    existing.CachedAt = DateTime.UtcNow;
                    updatedBars.Add(existing);
                }
                else
                {
                    newBars.Add(bar);
                }
            }

            // Bulk insert new bars
            if (newBars.Any())
            {
                await CachedStockBars.AddRangeAsync(newBars);
            }

            // Update metadata efficiently
            await UpdateCacheMetadataAsync(symbol, timeFrame, cachedBars);

            // Commit transaction
            await SaveChangesAsync();
            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// Efficiently updates cache metadata for a symbol and timeframe
    /// </summary>
    private async Task UpdateCacheMetadataAsync(string symbol, string timeFrame, List<CachedStockBar> newBars)
    {
        var cacheKey = StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var latestDate = newBars.Max(b => b.TimeUtc);
        var earliestDate = newBars.Min(b => b.TimeUtc);
        var metadata = await StockCacheMetadata.FindAsync(cacheKey);

        if (metadata == null)
        {
            metadata = new StockCacheMetadata
            {
                CacheKey = cacheKey,
                Symbol = symbol,
                TimeFrame = timeFrame,
                LatestDataDate = latestDate,
                EarliestDataDate = earliestDate,
                LastUpdated = DateTime.UtcNow,
                BarCount = newBars.Count
            };
            StockCacheMetadata.Add(metadata);
        }
        else
        {
            // Update date ranges
            if (latestDate > metadata.LatestDataDate)
            {
                metadata.LatestDataDate = latestDate;
            }
            if (earliestDate < metadata.EarliestDataDate)
            {
                metadata.EarliestDataDate = earliestDate;
            }
            metadata.LastUpdated = DateTime.UtcNow;

            // Use more efficient count query
            metadata.BarCount = await CachedStockBars
                .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame)
                .CountAsync();
        }
    }

    /// <summary>
    /// Bulk insert operation for multiple symbols and timeframes
    /// </summary>
    public async Task BulkInsertBarsAsync(IDictionary<string, IDictionary<string, IEnumerable<IBar>>> symbolTimeFrameBars)
    {
        using var transaction = await Database.BeginTransactionAsync();
        try
        {
            foreach (var symbolEntry in symbolTimeFrameBars)
            {
                var symbol = symbolEntry.Key;
                foreach (var timeFrameEntry in symbolEntry.Value)
                {
                    var timeFrame = timeFrameEntry.Key;
                    var bars = timeFrameEntry.Value;

                    await AddOrUpdateCachedBarsAsync(symbol, timeFrame, bars);
                }
            }

            await SaveChangesAsync();
            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// Cleans up old cached data (older than specified days) with optimized bulk operations
    /// </summary>
    public async Task CleanupOldDataAsync(int retainDays = 365)
    {
        using var transaction = await Database.BeginTransactionAsync();
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-retainDays);

            // Use ExecuteDeleteAsync for better performance (EF Core 7+)
            var deletedCount = await CachedStockBars
                .Where(b => b.TimeUtc < cutoffDate)
                .ExecuteDeleteAsync();

            // Update metadata after cleanup using bulk operations
            var affectedSymbols = await CachedStockBars
                .Select(b => new { b.Symbol, b.TimeFrame })
                .Distinct()
                .ToListAsync();

            var metadataUpdates = new List<StockCacheMetadata>();
            var metadataToRemove = new List<StockCacheMetadata>();

            foreach (var symbolTimeFrame in affectedSymbols)
            {
                var cacheKey = StockCacheMetadata.CreateCacheKey(symbolTimeFrame.Symbol, symbolTimeFrame.TimeFrame);
                var metadata = await StockCacheMetadata.FindAsync(cacheKey);

                if (metadata != null)
                {
                    // Use aggregation queries for better performance
                    var stats = await CachedStockBars
                        .Where(b => b.Symbol == symbolTimeFrame.Symbol && b.TimeFrame == symbolTimeFrame.TimeFrame)
                        .GroupBy(b => 1)
                        .Select(g => new
                        {
                            Count = g.Count(),
                            MinDate = g.Min(b => b.TimeUtc),
                            MaxDate = g.Max(b => b.TimeUtc)
                        })
                        .FirstOrDefaultAsync();

                    if (stats != null && stats.Count > 0)
                    {
                        metadata.EarliestDataDate = stats.MinDate;
                        metadata.LatestDataDate = stats.MaxDate;
                        metadata.BarCount = stats.Count;
                        metadata.LastUpdated = DateTime.UtcNow;
                        metadataUpdates.Add(metadata);
                    }
                    else
                    {
                        // No bars left, remove metadata
                        metadataToRemove.Add(metadata);
                    }
                }
            }

            // Bulk remove empty metadata
            if (metadataToRemove.Any())
            {
                StockCacheMetadata.RemoveRange(metadataToRemove);
            }

            await SaveChangesAsync();
            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// Gets cache statistics for monitoring
    /// </summary>
    public async Task<IDictionary<string, Services.CacheStats>> GetCacheStatsAsync()
    {
        var stats = new Dictionary<string, Services.CacheStats>();

        var metadataList = await StockCacheMetadata.ToListAsync();

        foreach (var metadata in metadataList)
        {
            // Estimate size (rough calculation)
            var estimatedSizeBytes = metadata.BarCount * 100; // Rough estimate: 100 bytes per bar

            var cacheStats = new Services.CacheStats(
                metadata.Symbol,
                metadata.TimeFrame,
                metadata.BarCount,
                metadata.EarliestDataDate,
                metadata.LatestDataDate,
                metadata.LastUpdated,
                estimatedSizeBytes
            );

            stats[metadata.CacheKey] = cacheStats;
        }

        return stats;
    }
}
