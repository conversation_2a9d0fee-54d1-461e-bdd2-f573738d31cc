namespace SmaTrendFollower.Services;

/// <summary>
/// Service for compressing and decompressing historical bar data to save storage space
/// </summary>
public interface IDataCompressionService
{
    /// <summary>
    /// Compresses bar data using Brotli compression
    /// </summary>
    /// <param name="data">Raw data to compress</param>
    /// <returns>Compressed data as byte array</returns>
    byte[] Compress(byte[] data);

    /// <summary>
    /// Decompresses bar data using Brotli compression
    /// </summary>
    /// <param name="compressedData">Compressed data to decompress</param>
    /// <returns>Decompressed data as byte array</returns>
    byte[] Decompress(byte[] compressedData);

    /// <summary>
    /// Compresses JSON string data
    /// </summary>
    /// <param name="jsonData">JSON string to compress</param>
    /// <returns>Compressed data as byte array</returns>
    byte[] CompressJson(string jsonData);

    /// <summary>
    /// Decompresses data to JSON string
    /// </summary>
    /// <param name="compressedData">Compressed data to decompress</param>
    /// <returns>Decompressed JSON string</returns>
    string DecompressToJson(byte[] compressedData);

    /// <summary>
    /// Calculates compression ratio for given data
    /// </summary>
    /// <param name="originalSize">Original data size in bytes</param>
    /// <param name="compressedSize">Compressed data size in bytes</param>
    /// <returns>Compression ratio (0.0 to 1.0, where lower is better compression)</returns>
    double GetCompressionRatio(int originalSize, int compressedSize);
}
