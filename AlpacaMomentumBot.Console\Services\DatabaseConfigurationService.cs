using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Data.Sqlite;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for configuring database connections with optimized settings for high-performance caching
/// </summary>
public sealed class DatabaseConfigurationService : IDatabaseConfigurationService
{
    private readonly ILogger<DatabaseConfigurationService> _logger;

    public DatabaseConfigurationService(ILogger<DatabaseConfigurationService> logger)
    {
        _logger = logger;
    }

    public void ConfigureSqlite(DbContextOptionsBuilder options, string connectionString)
    {
        options.UseSqlite(connectionString, sqliteOptions =>
        {
            // Enable command timeout for long-running operations
            sqliteOptions.CommandTimeout(30);
        })
        .EnableSensitiveDataLogging(false) // Disable in production
        .EnableServiceProviderCaching(true) // Cache service provider for better performance
        .EnableDetailedErrors(false) // Disable in production for performance
        .LogTo(message => _logger.LogDebug("EF Core: {Message}", message), LogLevel.Debug);

        // Configure query behavior for performance
        options.ConfigureWarnings(warnings =>
        {
            warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
        });
    }

    public string GetOptimizedConnectionString(string databasePath)
    {
        var builder = new SqliteConnectionStringBuilder
        {
            DataSource = databasePath,
            Mode = SqliteOpenMode.ReadWriteCreate,
            Cache = SqliteCacheMode.Shared, // Enable shared cache for better concurrency
            ForeignKeys = true, // Enable foreign key constraints
            Pooling = true, // Enable connection pooling
            DefaultTimeout = 30, // 30 second timeout
        };

        // Add performance-oriented pragma settings
        var connectionString = builder.ConnectionString;
        
        // Add custom pragma settings for performance
        connectionString += ";PRAGMA journal_mode=WAL;"; // Write-Ahead Logging for better concurrency
        connectionString += "PRAGMA synchronous=NORMAL;"; // Balance between safety and performance
        connectionString += "PRAGMA cache_size=10000;"; // 10MB cache size
        connectionString += "PRAGMA temp_store=MEMORY;"; // Store temporary tables in memory
        connectionString += "PRAGMA mmap_size=268435456;"; // 256MB memory-mapped I/O
        connectionString += "PRAGMA optimize;"; // Enable query optimizer

        return connectionString;
    }

    public async Task OptimizeDatabaseAsync(string connectionString)
    {
        try
        {
            _logger.LogInformation("Starting database optimization");

            using var connection = new SqliteConnection(connectionString);
            await connection.OpenAsync();

            // Execute optimization commands
            var optimizationCommands = new[]
            {
                "PRAGMA optimize;", // Analyze and optimize query plans
                "PRAGMA wal_checkpoint(TRUNCATE);", // Checkpoint WAL file
                "VACUUM;", // Rebuild database file to reclaim space
                "ANALYZE;", // Update table statistics for query optimizer
            };

            foreach (var command in optimizationCommands)
            {
                try
                {
                    using var cmd = new SqliteCommand(command, connection);
                    await cmd.ExecuteNonQueryAsync();
                    _logger.LogDebug("Executed optimization command: {Command}", command);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to execute optimization command: {Command}", command);
                }
            }

            _logger.LogInformation("Database optimization completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during database optimization");
            throw;
        }
    }
}
