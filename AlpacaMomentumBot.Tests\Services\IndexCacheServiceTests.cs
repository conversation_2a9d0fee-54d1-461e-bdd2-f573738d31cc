using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using FluentAssertions;
using Xunit;
using SmaTrendFollower.Data;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Tests.Services;

public class IndexCacheServiceTests : IDisposable
{
    private readonly IndexCacheDbContext _dbContext;
    private readonly Mock<ILogger<IndexCacheService>> _mockLogger;
    private readonly IndexCacheService _cacheService;

    public IndexCacheServiceTests()
    {
        // Use in-memory database for testing
        var options = new DbContextOptionsBuilder<IndexCacheDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _dbContext = new IndexCacheDbContext(options);
        _mockLogger = new Mock<ILogger<IndexCacheService>>();
        _cacheService = new IndexCacheService(_dbContext, _mockLogger.Object);
    }

    public void Dispose()
    {
        _dbContext.Dispose();
    }

    [Fact]
    public async Task InitializeCacheAsync_ShouldCreateDatabase()
    {
        // Act
        await _cacheService.InitializeCacheAsync();

        // Assert
        _dbContext.Database.EnsureCreated().Should().BeFalse(); // Already created
    }

    [Fact]
    public async Task CacheBarsAsync_ShouldStoreBarsInDatabase()
    {
        // Arrange
        var symbol = "I:SPX";
        var indexBars = new[]
        {
            new IndexBar(new DateTime(2024, 1, 1), 4700m, 4750m, 4680m, 4720m, 1000000),
            new IndexBar(new DateTime(2024, 1, 2), 4720m, 4780m, 4710m, 4760m, 1100000)
        };

        // Act
        await _cacheService.CacheBarsAsync(symbol, indexBars);

        // Assert
        var cachedBars = await _dbContext.CachedIndexBars.Where(b => b.Symbol == symbol).ToListAsync();
        cachedBars.Should().HaveCount(2);
        cachedBars[0].Close.Should().Be(4720m);
        cachedBars[1].Close.Should().Be(4760m);

        var metadata = await _dbContext.CacheMetadata.FindAsync(symbol);
        metadata.Should().NotBeNull();
        metadata!.BarCount.Should().Be(2);
        metadata.LatestDataDate.Should().Be(new DateTime(2024, 1, 2));
    }

    [Fact]
    public async Task GetCachedBarsAsync_ShouldReturnBarsInDateRange()
    {
        // Arrange
        var symbol = "I:VIX";
        var indexBars = new[]
        {
            new IndexBar(new DateTime(2024, 1, 1), 20.5m, 21.0m, 20.0m, 20.8m, 500000),
            new IndexBar(new DateTime(2024, 1, 2), 20.8m, 21.5m, 20.5m, 21.2m, 600000),
            new IndexBar(new DateTime(2024, 1, 3), 21.2m, 22.0m, 21.0m, 21.8m, 700000)
        };

        await _cacheService.CacheBarsAsync(symbol, indexBars);

        // Act
        var result = await _cacheService.GetCachedBarsAsync(symbol, new DateTime(2024, 1, 1), new DateTime(2024, 1, 2));

        // Assert
        var resultList = result.ToList();
        resultList.Should().HaveCount(2);
        resultList[0].TimeUtc.Should().Be(new DateTime(2024, 1, 1));
        resultList[1].TimeUtc.Should().Be(new DateTime(2024, 1, 2));
    }

    [Fact]
    public async Task GetLatestCachedDateAsync_ShouldReturnLatestDate()
    {
        // Arrange
        var symbol = "I:SPX";
        var indexBars = new[]
        {
            new IndexBar(new DateTime(2024, 1, 1), 4700m, 4750m, 4680m, 4720m, 1000000),
            new IndexBar(new DateTime(2024, 1, 3), 4720m, 4780m, 4710m, 4760m, 1100000)
        };

        await _cacheService.CacheBarsAsync(symbol, indexBars);

        // Act
        var latestDate = await _cacheService.GetLatestCachedDateAsync(symbol);

        // Assert
        latestDate.Should().Be(new DateTime(2024, 1, 3));
    }

    [Fact]
    public async Task GetLatestCachedDateAsync_ShouldReturnNullForUnknownSymbol()
    {
        // Act
        var latestDate = await _cacheService.GetLatestCachedDateAsync("UNKNOWN");

        // Assert
        latestDate.Should().BeNull();
    }

    [Fact]
    public async Task GetMissingDateRangeAsync_ShouldReturnNullWhenCacheIsFresh()
    {
        // Arrange
        var symbol = "I:SPX";
        var indexBars = new[]
        {
            new IndexBar(new DateTime(2024, 1, 1), 4700m, 4750m, 4680m, 4720m, 1000000),
            new IndexBar(new DateTime(2024, 1, 2), 4720m, 4780m, 4710m, 4760m, 1100000),
            new IndexBar(new DateTime(2024, 1, 3), 4760m, 4800m, 4740m, 4780m, 1200000)
        };

        await _cacheService.CacheBarsAsync(symbol, indexBars);

        // Act - Request data that's already cached
        var missingRange = await _cacheService.GetMissingDateRangeAsync(symbol, new DateTime(2024, 1, 1), new DateTime(2024, 1, 3));

        // Assert
        missingRange.Should().BeNull();
    }

    [Fact]
    public async Task GetMissingDateRangeAsync_ShouldReturnFullRangeForNewSymbol()
    {
        // Act
        var missingRange = await _cacheService.GetMissingDateRangeAsync("NEW_SYMBOL", new DateTime(2024, 1, 1), new DateTime(2024, 1, 5));

        // Assert
        missingRange.Should().NotBeNull();
        missingRange!.Value.startDate.Should().Be(new DateTime(2024, 1, 1));
        missingRange.Value.endDate.Should().Be(new DateTime(2024, 1, 5));
    }

    [Fact]
    public async Task GetMissingDateRangeAsync_ShouldReturnPartialRangeWhenCacheIsPartial()
    {
        // Arrange
        var symbol = "I:VIX";
        var indexBars = new[]
        {
            new IndexBar(new DateTime(2024, 1, 1), 20.5m, 21.0m, 20.0m, 20.8m, 500000),
            new IndexBar(new DateTime(2024, 1, 2), 20.8m, 21.5m, 20.5m, 21.2m, 600000)
        };

        await _cacheService.CacheBarsAsync(symbol, indexBars);

        // Act - Request data beyond what's cached
        var missingRange = await _cacheService.GetMissingDateRangeAsync(symbol, new DateTime(2024, 1, 1), new DateTime(2024, 1, 5));

        // Assert
        missingRange.Should().NotBeNull();
        missingRange!.Value.startDate.Should().Be(new DateTime(2024, 1, 3)); // Day after latest cached
        missingRange.Value.endDate.Should().Be(new DateTime(2024, 1, 5));
    }

    [Fact]
    public async Task IsCacheFreshAsync_ShouldReturnTrueWhenCacheIsUpToDate()
    {
        // Arrange
        var symbol = "I:SPX";
        var indexBars = new[]
        {
            new IndexBar(new DateTime(2024, 1, 1), 4700m, 4750m, 4680m, 4720m, 1000000),
            new IndexBar(new DateTime(2024, 1, 2), 4720m, 4780m, 4710m, 4760m, 1100000)
        };

        await _cacheService.CacheBarsAsync(symbol, indexBars);

        // Act
        var isFresh = await _cacheService.IsCacheFreshAsync(symbol, new DateTime(2024, 1, 2));

        // Assert
        isFresh.Should().BeTrue();
    }

    [Fact]
    public async Task IsCacheFreshAsync_ShouldReturnFalseWhenCacheIsStale()
    {
        // Arrange
        var symbol = "I:SPX";
        var indexBars = new[]
        {
            new IndexBar(new DateTime(2024, 1, 1), 4700m, 4750m, 4680m, 4720m, 1000000)
        };

        await _cacheService.CacheBarsAsync(symbol, indexBars);

        // Act
        var isFresh = await _cacheService.IsCacheFreshAsync(symbol, new DateTime(2024, 1, 5));

        // Assert
        isFresh.Should().BeFalse();
    }

    [Fact]
    public async Task CacheBarsAsync_ShouldHandleDuplicateBarsGracefully()
    {
        // Arrange
        var symbol = "I:SPX";
        var indexBar = new IndexBar(new DateTime(2024, 1, 1), 4700m, 4750m, 4680m, 4720m, 1000000);

        // Act - Cache the same bar twice
        await _cacheService.CacheBarsAsync(symbol, new[] { indexBar });
        await _cacheService.CacheBarsAsync(symbol, new[] { indexBar });

        // Assert - Should only have one bar
        var cachedBars = await _dbContext.CachedIndexBars.Where(b => b.Symbol == symbol).ToListAsync();
        cachedBars.Should().HaveCount(1);
    }

    [Fact]
    public async Task PerformMaintenanceAsync_ShouldRemoveOldData()
    {
        // Arrange
        var symbol = "I:SPX";
        var oldDate = DateTime.UtcNow.AddDays(-400); // Older than retention period
        var recentDate = DateTime.UtcNow.AddDays(-10); // Within retention period

        var indexBars = new[]
        {
            new IndexBar(oldDate, 4700m, 4750m, 4680m, 4720m, 1000000),
            new IndexBar(recentDate, 4720m, 4780m, 4710m, 4760m, 1100000)
        };

        await _cacheService.CacheBarsAsync(symbol, indexBars);

        // Act
        await _cacheService.PerformMaintenanceAsync(retainDays: 365);

        // Assert
        var remainingBars = await _dbContext.CachedIndexBars.Where(b => b.Symbol == symbol).ToListAsync();
        remainingBars.Should().HaveCount(1);
        remainingBars[0].TimeUtc.Should().Be(recentDate);
    }
}
