namespace SmaTrendFollower.Services;

/// <summary>
/// Service for managing compression of cached bar data to optimize storage
/// </summary>
public interface ICacheCompressionService
{
    /// <summary>
    /// Compresses bars older than the specified number of days
    /// </summary>
    /// <param name="olderThanDays">Compress bars older than this many days (default: 30)</param>
    /// <returns>Number of bars compressed and total space saved in bytes</returns>
    Task<(int barsCompressed, long bytesSaved)> CompressOldBarsAsync(int olderThanDays = 30);

    /// <summary>
    /// Decompresses all compressed bars (useful for maintenance or migration)
    /// </summary>
    /// <returns>Number of bars decompressed</returns>
    Task<int> DecompressAllBarsAsync();

    /// <summary>
    /// Gets compression statistics for monitoring
    /// </summary>
    /// <returns>Compression statistics</returns>
    Task<CompressionStats> GetCompressionStatsAsync();

    /// <summary>
    /// Estimates potential space savings if compression were applied to all eligible bars
    /// </summary>
    /// <param name="olderThanDays">Consider bars older than this many days</param>
    /// <returns>Estimated space savings in bytes</returns>
    Task<long> EstimateCompressionSavingsAsync(int olderThanDays = 30);
}

/// <summary>
/// Statistics about cache compression
/// </summary>
public readonly record struct CompressionStats(
    int TotalBars,
    int CompressedBars,
    int UncompressedBars,
    long TotalOriginalSize,
    long TotalCompressedSize,
    double AverageCompressionRatio,
    long SpaceSaved
);
