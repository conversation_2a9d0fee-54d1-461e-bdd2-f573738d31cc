# PowerShell script to update all AlpacaMomentumBot namespaces to SmaTrendFollower

Write-Host "Updating namespaces from AlpacaMomentumBot to SmaTrendFollower..."

# Update namespace declarations
Get-ChildItem -Path "AlpacaMomentumBot.Console" -Recurse -Include "*.cs" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    if ($content -match "namespace AlpacaMomentumBot\.") {
        $newContent = $content -replace "namespace AlpacaMomentumBot\.", "namespace SmaTrendFollower."
        Set-Content -Path $_.FullName -Value $newContent -NoNewline
        Write-Host "Updated namespace in: $($_.FullName)"
    }
}

# Update using statements
Get-ChildItem -Path "AlpacaMomentumBot.Console" -Recurse -Include "*.cs" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    if ($content -match "using AlpacaMomentumBot\.") {
        $newContent = $content -replace "using AlpacaMomentumBot\.", "using SmaTrendFollower."
        Set-Content -Path $_.FullName -Value $newContent -NoNewline
        Write-Host "Updated using statements in: $($_.FullName)"
    }
}

# Update test files
Get-ChildItem -Path "AlpacaMomentumBot.Tests" -Recurse -Include "*.cs" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    if ($content -match "namespace AlpacaMomentumBot\.") {
        $newContent = $content -replace "namespace AlpacaMomentumBot\.", "namespace SmaTrendFollower."
        Set-Content -Path $_.FullName -Value $newContent -NoNewline
        Write-Host "Updated namespace in test: $($_.FullName)"
    }
}

Get-ChildItem -Path "AlpacaMomentumBot.Tests" -Recurse -Include "*.cs" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    if ($content -match "using AlpacaMomentumBot\.") {
        $newContent = $content -replace "using AlpacaMomentumBot\.", "using SmaTrendFollower."
        Set-Content -Path $_.FullName -Value $newContent -NoNewline
        Write-Host "Updated using statements in test: $($_.FullName)"
    }
}

# Update Examples folder
Get-ChildItem -Path "Examples" -Recurse -Include "*.cs" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    if ($content -match "using AlpacaMomentumBot\.") {
        $newContent = $content -replace "using AlpacaMomentumBot\.", "using SmaTrendFollower."
        Set-Content -Path $_.FullName -Value $newContent -NoNewline
        Write-Host "Updated using statements in example: $($_.FullName)"
    }
}

Write-Host "Namespace updates completed!"
