using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Console commands for cache management and monitoring
/// </summary>
public sealed class CacheConsoleCommands
{
    private readonly ICacheManagementService _managementService;
    private readonly ICacheMetricsService _metricsService;
    private readonly ICacheWarmingService _warmingService;
    private readonly ILogger<CacheConsoleCommands> _logger;

    public CacheConsoleCommands(
        ICacheManagementService managementService,
        ICacheMetricsService metricsService,
        ICacheWarmingService warmingService,
        ILogger<CacheConsoleCommands> logger)
    {
        _managementService = managementService;
        _metricsService = metricsService;
        _warmingService = warmingService;
        _logger = logger;
    }

    /// <summary>
    /// Displays cache dashboard information
    /// </summary>
    public async Task ShowDashboardAsync()
    {
        try
        {
            Console.WriteLine("=== SmaTrendFollower Cache Dashboard ===");
            Console.WriteLine();

            var dashboard = await _managementService.GetDashboardDataAsync();

            // Overview
            Console.WriteLine("📊 CACHE OVERVIEW");
            Console.WriteLine($"   Total Symbols: {dashboard.Overview.TotalSymbols:N0}");
            Console.WriteLine($"   Total Bars: {dashboard.Overview.TotalBars:N0}");
            Console.WriteLine($"   Total Size: {FormatBytes(dashboard.Overview.TotalSizeBytes)}");
            Console.WriteLine($"   Compressed Size: {FormatBytes(dashboard.Overview.CompressedSizeBytes)}");
            Console.WriteLine($"   Compression Ratio: {dashboard.Overview.CompressionRatio:P2}");
            Console.WriteLine($"   Data Span: {dashboard.Overview.DataSpan.Days} days");
            Console.WriteLine();

            // Performance
            Console.WriteLine("⚡ PERFORMANCE METRICS");
            Console.WriteLine($"   Cache Hit Ratio: {dashboard.Performance.CacheHitRatio:P2}");
            Console.WriteLine($"   Total Requests: {dashboard.Performance.TotalRequests:N0}");
            Console.WriteLine($"   Cache Hits: {dashboard.Performance.CacheHits:N0}");
            Console.WriteLine($"   Cache Misses: {dashboard.Performance.CacheMisses:N0}");
            Console.WriteLine($"   Avg Cache Response: {dashboard.Performance.AverageCacheResponseTimeMs:F2}ms");
            Console.WriteLine($"   Avg API Response: {dashboard.Performance.AverageApiResponseTimeMs:F2}ms");
            Console.WriteLine($"   API Throttle Events: {dashboard.Performance.ApiThrottleEvents:N0}");
            Console.WriteLine();

            // Health
            Console.WriteLine("🏥 CACHE HEALTH");
            Console.WriteLine($"   Overall Health: {dashboard.Health.OverallHealth}");
            Console.WriteLine($"   Health Score: {dashboard.Health.HealthScore:P1}");
            foreach (var metric in dashboard.Health.Metrics.Take(3))
            {
                var status = metric.Status switch
                {
                    "Good" => "✅",
                    "Warning" => "⚠️",
                    "Critical" => "❌",
                    _ => "❓"
                };
                Console.WriteLine($"   {status} {metric.Name}: {metric.Value:F2} (threshold: {metric.Threshold:F2})");
            }
            Console.WriteLine();

            // Top Symbols
            Console.WriteLine("🔥 TOP SYMBOLS (by requests)");
            foreach (var symbol in dashboard.TopSymbols.Take(10))
            {
                Console.WriteLine($"   {symbol.Key}: {symbol.Value.TotalBars:N0} bars, {symbol.Value.CacheHitRatio:P1} hit ratio");
            }
            Console.WriteLine();

            Console.WriteLine($"Last Updated: {dashboard.LastUpdated:yyyy-MM-dd HH:mm:ss} UTC");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error displaying cache dashboard");
            Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Performs cache maintenance
    /// </summary>
    public async Task RunMaintenanceAsync(string mode = "standard")
    {
        try
        {
            Console.WriteLine($"🔧 Starting cache maintenance ({mode})...");

            var options = mode.ToLower() switch
            {
                "quick" => CacheMaintenanceDefaults.Quick,
                "full" => CacheMaintenanceDefaults.Full,
                _ => CacheMaintenanceDefaults.Standard
            };

            var result = await _managementService.PerformMaintenanceAsync(options);

            Console.WriteLine($"✅ Maintenance completed in {result.Duration.TotalSeconds:F1}s");
            Console.WriteLine($"   Success: {result.Success}");
            Console.WriteLine($"   Data Cleaned: {FormatBytes(result.DataCleaned)}");
            Console.WriteLine($"   Bars Compressed: {result.BarsCompressed:N0}");
            Console.WriteLine($"   Space Saved: {FormatBytes(result.SpaceSaved)}");
            Console.WriteLine($"   Operations: {result.Operations.Length}");

            if (result.Errors.Any())
            {
                Console.WriteLine("⚠️ Errors:");
                foreach (var error in result.Errors)
                {
                    Console.WriteLine($"   - {error}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache maintenance");
            Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates cache integrity
    /// </summary>
    public async Task ValidateCacheAsync()
    {
        try
        {
            Console.WriteLine("🔍 Validating cache integrity...");

            var result = await _managementService.ValidateCacheIntegrityAsync();

            Console.WriteLine($"✅ Validation completed");
            Console.WriteLine($"   Valid: {result.IsValid}");
            Console.WriteLine($"   Total Checks: {result.TotalChecks}");
            Console.WriteLine($"   Issues Found: {result.IssuesFound}");

            if (result.Issues.Any())
            {
                Console.WriteLine("\n📋 Issues:");
                foreach (var issue in result.Issues.Take(10))
                {
                    var icon = issue.Severity switch
                    {
                        "Error" => "❌",
                        "Warning" => "⚠️",
                        _ => "ℹ️"
                    };
                    Console.WriteLine($"   {icon} [{issue.Category}] {issue.Description}");
                    if (!string.IsNullOrEmpty(issue.Symbol))
                    {
                        Console.WriteLine($"      Symbol: {issue.Symbol} {issue.TimeFrame}");
                    }
                }

                if (result.Issues.Length > 10)
                {
                    Console.WriteLine($"   ... and {result.Issues.Length - 10} more issues");
                }
            }

            if (result.Recommendations.Any())
            {
                Console.WriteLine("\n💡 Recommendations:");
                foreach (var recommendation in result.Recommendations)
                {
                    Console.WriteLine($"   - {recommendation}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache validation");
            Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Refreshes cache for specific symbols
    /// </summary>
    public async Task RefreshSymbolsAsync(string symbolsInput, int days = 30)
    {
        try
        {
            var symbols = symbolsInput.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                    .Select(s => s.Trim().ToUpper())
                                    .ToArray();

            Console.WriteLine($"🔄 Refreshing cache for {symbols.Length} symbols ({days} days)...");

            var result = await _managementService.RefreshSymbolsAsync(symbols, null, days);

            Console.WriteLine($"✅ Refresh completed in {result.Duration.TotalSeconds:F1}s");
            Console.WriteLine($"   Processed: {result.SymbolsProcessed}");
            Console.WriteLine($"   Successful: {result.SymbolsSuccessful}");
            Console.WriteLine($"   Failed: {result.SymbolsFailed}");
            Console.WriteLine($"   New Bars: {result.NewBarsAdded:N0}");
            Console.WriteLine($"   Updated Bars: {result.BarsUpdated:N0}");

            if (result.FailedSymbols.Any())
            {
                Console.WriteLine("\n❌ Failed Symbols:");
                foreach (var symbol in result.FailedSymbols)
                {
                    Console.WriteLine($"   - {symbol}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during symbol refresh");
            Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Runs cache warming
    /// </summary>
    public async Task WarmCacheAsync(string type = "essential")
    {
        try
        {
            Console.WriteLine($"🔥 Starting cache warming ({type})...");

            var (symbolsWarmed, barsWarmed) = type.ToLower() switch
            {
                "essential" => await _warmingService.WarmEssentialSymbolsAsync(),
                _ => await _warmingService.WarmEssentialSymbolsAsync()
            };

            Console.WriteLine($"✅ Cache warming completed");
            Console.WriteLine($"   Symbols Warmed: {symbolsWarmed}");
            Console.WriteLine($"   Bars Cached: {barsWarmed:N0}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache warming");
            Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Shows cache metrics in JSON format
    /// </summary>
    public void ShowMetricsJson()
    {
        try
        {
            var json = _metricsService.ExportMetricsAsJson();
            Console.WriteLine("📊 Cache Metrics (JSON):");
            Console.WriteLine(json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting metrics");
            Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Resets cache metrics
    /// </summary>
    public void ResetMetrics()
    {
        try
        {
            _metricsService.ResetMetrics();
            Console.WriteLine("✅ Cache metrics reset");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting metrics");
            Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Shows available cache commands
    /// </summary>
    public void ShowHelp()
    {
        Console.WriteLine("=== SmaTrendFollower Cache Commands ===");
        Console.WriteLine();
        Console.WriteLine("📊 dashboard              - Show cache dashboard");
        Console.WriteLine("🔧 maintenance [mode]     - Run maintenance (quick/standard/full)");
        Console.WriteLine("🔍 validate               - Validate cache integrity");
        Console.WriteLine("🔄 refresh <symbols>      - Refresh symbols (comma-separated)");
        Console.WriteLine("🔥 warm [type]            - Warm cache (essential)");
        Console.WriteLine("📈 metrics                - Show metrics in JSON");
        Console.WriteLine("🔄 reset-metrics          - Reset performance metrics");
        Console.WriteLine("❓ help                   - Show this help");
        Console.WriteLine();
        Console.WriteLine("Examples:");
        Console.WriteLine("  cache dashboard");
        Console.WriteLine("  cache maintenance full");
        Console.WriteLine("  cache refresh SPY,QQQ,AAPL");
        Console.WriteLine("  cache warm essential");
    }

    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";
        
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        
        return $"{size:F2} {sizes[order]}";
    }
}
