using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for managing compression of cached bar data to optimize storage space
/// Compresses bars older than 30 days while keeping recent data uncompressed for fast access
/// </summary>
public sealed class CacheCompressionService : ICacheCompressionService
{
    private readonly StockBarCacheDbContext _dbContext;
    private readonly IDataCompressionService _compressionService;
    private readonly ILogger<CacheCompressionService> _logger;

    public CacheCompressionService(
        StockBarCacheDbContext dbContext,
        IDataCompressionService compressionService,
        ILogger<CacheCompressionService> logger)
    {
        _dbContext = dbContext;
        _compressionService = compressionService;
        _logger = logger;
    }

    public async Task<(int barsCompressed, long bytesSaved)> CompressOldBarsAsync(int olderThanDays = 30)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-olderThanDays);
        
        _logger.LogInformation("Starting compression of bars older than {CutoffDate:yyyy-MM-dd}", cutoffDate);

        using var transaction = await _dbContext.Database.BeginTransactionAsync();
        try
        {
            // Get uncompressed bars older than cutoff date
            var oldBars = await _dbContext.CachedStockBars
                .Where(b => b.TimeUtc < cutoffDate && !b.IsCompressed)
                .ToListAsync();

            if (!oldBars.Any())
            {
                _logger.LogInformation("No bars found for compression");
                return (0, 0);
            }

            _logger.LogInformation("Found {Count} bars eligible for compression", oldBars.Count);

            int compressedCount = 0;
            long totalBytesSaved = 0;

            // Process in batches to avoid memory issues
            const int batchSize = 1000;
            for (int i = 0; i < oldBars.Count; i += batchSize)
            {
                var batch = oldBars.Skip(i).Take(batchSize);
                
                foreach (var bar in batch)
                {
                    try
                    {
                        // Create JSON representation of bar data
                        var barData = new
                        {
                            Open = bar.Open,
                            High = bar.High,
                            Low = bar.Low,
                            Close = bar.Close,
                            Volume = bar.Volume,
                            Vwap = bar.Vwap,
                            TradeCount = bar.TradeCount
                        };

                        var jsonData = JsonSerializer.Serialize(barData);
                        var originalSize = System.Text.Encoding.UTF8.GetByteCount(jsonData);
                        var compressedData = _compressionService.CompressJson(jsonData);

                        // Update bar with compressed data
                        bar.IsCompressed = true;
                        bar.CompressedData = compressedData;
                        bar.OriginalDataSize = originalSize;

                        // Clear uncompressed fields to save space
                        bar.Open = 0;
                        bar.High = 0;
                        bar.Low = 0;
                        bar.Close = 0;
                        bar.Volume = 0;
                        bar.Vwap = null;
                        bar.TradeCount = null;

                        var bytesSaved = originalSize - compressedData.Length;
                        totalBytesSaved += bytesSaved;
                        compressedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to compress bar for {Symbol} at {TimeUtc}", 
                            bar.Symbol, bar.TimeUtc);
                    }
                }

                // Save batch
                await _dbContext.SaveChangesAsync();
                
                _logger.LogDebug("Compressed batch {BatchStart}-{BatchEnd} of {Total}", 
                    i + 1, Math.Min(i + batchSize, oldBars.Count), oldBars.Count);
            }

            await transaction.CommitAsync();

            _logger.LogInformation("Compression completed: {CompressedCount} bars compressed, {BytesSaved:N0} bytes saved",
                compressedCount, totalBytesSaved);

            return (compressedCount, totalBytesSaved);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error during bar compression");
            throw;
        }
    }

    public async Task<int> DecompressAllBarsAsync()
    {
        _logger.LogInformation("Starting decompression of all compressed bars");

        using var transaction = await _dbContext.Database.BeginTransactionAsync();
        try
        {
            var compressedBars = await _dbContext.CachedStockBars
                .Where(b => b.IsCompressed && b.CompressedData != null)
                .ToListAsync();

            if (!compressedBars.Any())
            {
                _logger.LogInformation("No compressed bars found");
                return 0;
            }

            _logger.LogInformation("Found {Count} compressed bars to decompress", compressedBars.Count);

            int decompressedCount = 0;

            foreach (var bar in compressedBars)
            {
                try
                {
                    var jsonData = _compressionService.DecompressToJson(bar.CompressedData!);
                    var barData = JsonSerializer.Deserialize<BarData>(jsonData);

                    if (barData != null)
                    {
                        // Restore uncompressed data
                        bar.Open = barData.Open;
                        bar.High = barData.High;
                        bar.Low = barData.Low;
                        bar.Close = barData.Close;
                        bar.Volume = barData.Volume;
                        bar.Vwap = barData.Vwap;
                        bar.TradeCount = barData.TradeCount;

                        // Clear compressed data
                        bar.IsCompressed = false;
                        bar.CompressedData = null;
                        bar.OriginalDataSize = null;

                        decompressedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to decompress bar for {Symbol} at {TimeUtc}", 
                        bar.Symbol, bar.TimeUtc);
                }
            }

            await _dbContext.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("Decompression completed: {DecompressedCount} bars decompressed", decompressedCount);

            return decompressedCount;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error during bar decompression");
            throw;
        }
    }

    public async Task<CompressionStats> GetCompressionStatsAsync()
    {
        try
        {
            var stats = await _dbContext.CachedStockBars
                .GroupBy(b => 1)
                .Select(g => new
                {
                    TotalBars = g.Count(),
                    CompressedBars = g.Count(b => b.IsCompressed),
                    TotalOriginalSize = g.Where(b => b.OriginalDataSize.HasValue).Sum(b => b.OriginalDataSize!.Value),
                    TotalCompressedSize = g.Where(b => b.CompressedData != null).Sum(b => b.CompressedData!.Length)
                })
                .FirstOrDefaultAsync();

            if (stats == null)
            {
                return new CompressionStats(0, 0, 0, 0, 0, 0.0, 0);
            }

            var uncompressedBars = stats.TotalBars - stats.CompressedBars;
            var spaceSaved = stats.TotalOriginalSize - stats.TotalCompressedSize;
            var averageCompressionRatio = stats.TotalOriginalSize > 0 
                ? (double)stats.TotalCompressedSize / stats.TotalOriginalSize 
                : 0.0;

            return new CompressionStats(
                stats.TotalBars,
                stats.CompressedBars,
                uncompressedBars,
                stats.TotalOriginalSize,
                stats.TotalCompressedSize,
                averageCompressionRatio,
                spaceSaved
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting compression statistics");
            return new CompressionStats(0, 0, 0, 0, 0, 0.0, 0);
        }
    }

    public async Task<long> EstimateCompressionSavingsAsync(int olderThanDays = 30)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-olderThanDays);
            
            var eligibleBars = await _dbContext.CachedStockBars
                .Where(b => b.TimeUtc < cutoffDate && !b.IsCompressed)
                .CountAsync();

            // Estimate based on average bar size and typical compression ratio
            const int averageBarSizeBytes = 100; // Rough estimate for JSON bar data
            const double typicalCompressionRatio = 0.3; // Brotli typically achieves 70% compression
            
            var estimatedOriginalSize = eligibleBars * averageBarSizeBytes;
            var estimatedCompressedSize = (long)(estimatedOriginalSize * typicalCompressionRatio);
            var estimatedSavings = estimatedOriginalSize - estimatedCompressedSize;

            _logger.LogDebug("Estimated compression savings: {EligibleBars} bars, {Savings:N0} bytes", 
                eligibleBars, estimatedSavings);

            return estimatedSavings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error estimating compression savings");
            return 0;
        }
    }

    private class BarData
    {
        public decimal Open { get; set; }
        public decimal High { get; set; }
        public decimal Low { get; set; }
        public decimal Close { get; set; }
        public long Volume { get; set; }
        public decimal? Vwap { get; set; }
        public ulong? TradeCount { get; set; }
    }
}
