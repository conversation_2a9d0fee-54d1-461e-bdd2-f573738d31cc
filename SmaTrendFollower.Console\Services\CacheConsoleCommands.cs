using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Console commands for cache management and monitoring
/// </summary>
public sealed class CacheConsoleCommands
{
    private readonly ICacheManagementService _managementService;
    private readonly ICacheMetricsService _metricsService;
    private readonly ICacheWarmingService _warmingService;
    private readonly ILogger<CacheConsoleCommands> _logger;

    public CacheConsoleCommands(
        ICacheManagementService managementService,
        ICacheMetricsService metricsService,
        ICacheWarmingService warmingService,
        ILogger<CacheConsoleCommands> logger)
    {
        _managementService = managementService;
        _metricsService = metricsService;
        _warmingService = warmingService;
        _logger = logger;
    }

    /// <summary>
    /// Displays cache dashboard information
    /// </summary>
    public async Task ShowDashboardAsync()
    {
        try
        {
            System.System.Console.WriteLine("=== SmaTrendFollower Cache Dashboard ===");
            System.System.Console.WriteLine();

            var dashboard = await _managementService.GetDashboardDataAsync();

            // Overview
            System.System.Console.WriteLine("📊 CACHE OVERVIEW");
            System.System.Console.WriteLine($"   Total Symbols: {dashboard.Overview.TotalSymbols:N0}");
            System.System.Console.WriteLine($"   Total Bars: {dashboard.Overview.TotalBars:N0}");
            System.System.Console.WriteLine($"   Total Size: {FormatBytes(dashboard.Overview.TotalSizeBytes)}");
            System.System.Console.WriteLine($"   Compressed Size: {FormatBytes(dashboard.Overview.CompressedSizeBytes)}");
            System.System.Console.WriteLine($"   Compression Ratio: {dashboard.Overview.CompressionRatio:P2}");
            System.System.Console.WriteLine($"   Data Span: {dashboard.Overview.DataSpan.Days} days");
            System.System.Console.WriteLine();

            // Performance
            System.System.Console.WriteLine("⚡ PERFORMANCE METRICS");
            System.System.Console.WriteLine($"   Cache Hit Ratio: {dashboard.Performance.CacheHitRatio:P2}");
            System.System.Console.WriteLine($"   Total Requests: {dashboard.Performance.TotalRequests:N0}");
            System.System.Console.WriteLine($"   Cache Hits: {dashboard.Performance.CacheHits:N0}");
            System.System.Console.WriteLine($"   Cache Misses: {dashboard.Performance.CacheMisses:N0}");
            System.System.Console.WriteLine($"   Avg Cache Response: {dashboard.Performance.AverageCacheResponseTimeMs:F2}ms");
            System.System.Console.WriteLine($"   Avg API Response: {dashboard.Performance.AverageApiResponseTimeMs:F2}ms");
            System.System.Console.WriteLine($"   API Throttle Events: {dashboard.Performance.ApiThrottleEvents:N0}");
            System.System.Console.WriteLine();

            // Health
            System.System.Console.WriteLine("🏥 CACHE HEALTH");
            System.System.Console.WriteLine($"   Overall Health: {dashboard.Health.OverallHealth}");
            System.System.Console.WriteLine($"   Health Score: {dashboard.Health.HealthScore:P1}");
            foreach (var metric in dashboard.Health.Metrics.Take(3))
            {
                var status = metric.Status switch
                {
                    "Good" => "✅",
                    "Warning" => "⚠️",
                    "Critical" => "❌",
                    _ => "❓"
                };
                System.System.Console.WriteLine($"   {status} {metric.Name}: {metric.Value:F2} (threshold: {metric.Threshold:F2})");
            }
            System.System.Console.WriteLine();

            // Top Symbols
            System.System.Console.WriteLine("🔥 TOP SYMBOLS (by requests)");
            foreach (var symbol in dashboard.TopSymbols.Take(10))
            {
                System.System.Console.WriteLine($"   {symbol.Key}: {symbol.Value.TotalBars:N0} bars, {symbol.Value.CacheHitRatio:P1} hit ratio");
            }
            System.System.Console.WriteLine();

            System.System.Console.WriteLine($"Last Updated: {dashboard.LastUpdated:yyyy-MM-dd HH:mm:ss} UTC");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error displaying cache dashboard");
            System.Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Performs cache maintenance
    /// </summary>
    public async Task RunMaintenanceAsync(string mode = "standard")
    {
        try
        {
            System.Console.WriteLine($"🔧 Starting cache maintenance ({mode})...");

            var options = mode.ToLower() switch
            {
                "quick" => CacheMaintenanceDefaults.Quick,
                "full" => CacheMaintenanceDefaults.Full,
                _ => CacheMaintenanceDefaults.Standard
            };

            var result = await _managementService.PerformMaintenanceAsync(options);

            System.Console.WriteLine($"✅ Maintenance completed in {result.Duration.TotalSeconds:F1}s");
            System.Console.WriteLine($"   Success: {result.Success}");
            System.Console.WriteLine($"   Data Cleaned: {FormatBytes(result.DataCleaned)}");
            System.Console.WriteLine($"   Bars Compressed: {result.BarsCompressed:N0}");
            System.Console.WriteLine($"   Space Saved: {FormatBytes(result.SpaceSaved)}");
            System.Console.WriteLine($"   Operations: {result.Operations.Length}");

            if (result.Errors.Any())
            {
                System.Console.WriteLine("⚠️ Errors:");
                foreach (var error in result.Errors)
                {
                    System.Console.WriteLine($"   - {error}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache maintenance");
            System.Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates cache integrity
    /// </summary>
    public async Task ValidateCacheAsync()
    {
        try
        {
            System.Console.WriteLine("🔍 Validating cache integrity...");

            var result = await _managementService.ValidateCacheIntegrityAsync();

            System.Console.WriteLine($"✅ Validation completed");
            System.Console.WriteLine($"   Valid: {result.IsValid}");
            System.Console.WriteLine($"   Total Checks: {result.TotalChecks}");
            System.Console.WriteLine($"   Issues Found: {result.IssuesFound}");

            if (result.Issues.Any())
            {
                System.Console.WriteLine("\n📋 Issues:");
                foreach (var issue in result.Issues.Take(10))
                {
                    var icon = issue.Severity switch
                    {
                        "Error" => "❌",
                        "Warning" => "⚠️",
                        _ => "ℹ️"
                    };
                    System.Console.WriteLine($"   {icon} [{issue.Category}] {issue.Description}");
                    if (!string.IsNullOrEmpty(issue.Symbol))
                    {
                        System.Console.WriteLine($"      Symbol: {issue.Symbol} {issue.TimeFrame}");
                    }
                }

                if (result.Issues.Length > 10)
                {
                    System.Console.WriteLine($"   ... and {result.Issues.Length - 10} more issues");
                }
            }

            if (result.Recommendations.Any())
            {
                System.Console.WriteLine("\n💡 Recommendations:");
                foreach (var recommendation in result.Recommendations)
                {
                    System.Console.WriteLine($"   - {recommendation}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache validation");
            System.Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Refreshes cache for specific symbols
    /// </summary>
    public async Task RefreshSymbolsAsync(string symbolsInput, int days = 30)
    {
        try
        {
            var symbols = symbolsInput.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                    .Select(s => s.Trim().ToUpper())
                                    .ToArray();

            System.Console.WriteLine($"🔄 Refreshing cache for {symbols.Length} symbols ({days} days)...");

            var result = await _managementService.RefreshSymbolsAsync(symbols, null, days);

            System.Console.WriteLine($"✅ Refresh completed in {result.Duration.TotalSeconds:F1}s");
            System.Console.WriteLine($"   Processed: {result.SymbolsProcessed}");
            System.Console.WriteLine($"   Successful: {result.SymbolsSuccessful}");
            System.Console.WriteLine($"   Failed: {result.SymbolsFailed}");
            System.Console.WriteLine($"   New Bars: {result.NewBarsAdded:N0}");
            System.Console.WriteLine($"   Updated Bars: {result.BarsUpdated:N0}");

            if (result.FailedSymbols.Any())
            {
                System.Console.WriteLine("\n❌ Failed Symbols:");
                foreach (var symbol in result.FailedSymbols)
                {
                    System.Console.WriteLine($"   - {symbol}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during symbol refresh");
            System.Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Runs cache warming
    /// </summary>
    public async Task WarmCacheAsync(string type = "essential")
    {
        try
        {
            System.Console.WriteLine($"🔥 Starting cache warming ({type})...");

            var (symbolsWarmed, barsWarmed) = type.ToLower() switch
            {
                "essential" => await _warmingService.WarmEssentialSymbolsAsync(),
                _ => await _warmingService.WarmEssentialSymbolsAsync()
            };

            System.Console.WriteLine($"✅ Cache warming completed");
            System.Console.WriteLine($"   Symbols Warmed: {symbolsWarmed}");
            System.Console.WriteLine($"   Bars Cached: {barsWarmed:N0}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache warming");
            System.Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Shows cache metrics in JSON format
    /// </summary>
    public void ShowMetricsJson()
    {
        try
        {
            var json = _metricsService.ExportMetricsAsJson();
            System.Console.WriteLine("📊 Cache Metrics (JSON):");
            System.Console.WriteLine(json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting metrics");
            System.Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Resets cache metrics
    /// </summary>
    public void ResetMetrics()
    {
        try
        {
            _metricsService.ResetMetrics();
            System.Console.WriteLine("✅ Cache metrics reset");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting metrics");
            System.Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Shows available cache commands
    /// </summary>
    public void ShowHelp()
    {
        System.Console.WriteLine("=== SmaTrendFollower Cache Commands ===");
        System.Console.WriteLine();
        System.Console.WriteLine("📊 dashboard              - Show cache dashboard");
        System.Console.WriteLine("🔧 maintenance [mode]     - Run maintenance (quick/standard/full)");
        System.Console.WriteLine("🔍 validate               - Validate cache integrity");
        System.Console.WriteLine("🔄 refresh <symbols>      - Refresh symbols (comma-separated)");
        System.Console.WriteLine("🔥 warm [type]            - Warm cache (essential)");
        System.Console.WriteLine("📈 metrics                - Show metrics in JSON");
        System.Console.WriteLine("🔄 reset-metrics          - Reset performance metrics");
        System.Console.WriteLine("❓ help                   - Show this help");
        System.Console.WriteLine();
        System.Console.WriteLine("Examples:");
        System.Console.WriteLine("  cache dashboard");
        System.Console.WriteLine("  cache maintenance full");
        System.Console.WriteLine("  cache refresh SPY,QQQ,AAPL");
        System.Console.WriteLine("  cache warm essential");
    }

    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";
        
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        
        return $"{size:F2} {sizes[order]}";
    }
}
