using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for real-time streaming data from Alpaca and Polygon websockets
/// </summary>
public interface IStreamingDataService : IDisposable
{
    // === Events for Real-time Data ===
    
    /// <summary>
    /// Fired when a real-time quote is received for equity symbols
    /// </summary>
    event EventHandler<StreamingQuoteEventArgs>? QuoteReceived;
    
    /// <summary>
    /// Fired when a real-time bar is received for equity symbols
    /// </summary>
    event EventHandler<StreamingBarEventArgs>? BarReceived;
    
    /// <summary>
    /// Fired when an index value update is received (VIX spikes, etc.)
    /// </summary>
    event EventHandler<IndexUpdateEventArgs>? IndexUpdated;
    
    /// <summary>
    /// Fired when a trade execution/fill is received
    /// </summary>
    event EventHandler<TradeUpdateEventArgs>? TradeUpdated;
    
    // === Connection Management ===
    
    /// <summary>
    /// Connects to Alpaca streaming for equity data
    /// </summary>
    Task ConnectAlpacaStreamAsync();
    
    /// <summary>
    /// Connects to Polygon streaming for index/volatility data
    /// </summary>
    Task ConnectPolygonStreamAsync();
    
    /// <summary>
    /// Disconnects all streaming connections
    /// </summary>
    Task DisconnectAllAsync();
    
    // === Subscription Management ===
    
    /// <summary>
    /// Subscribe to real-time quotes for equity symbols (Alpaca)
    /// </summary>
    Task SubscribeToQuotesAsync(IEnumerable<string> symbols);
    
    /// <summary>
    /// Subscribe to real-time bars for equity symbols (Alpaca)
    /// </summary>
    Task SubscribeToBarsAsync(IEnumerable<string> symbols);
    
    /// <summary>
    /// Subscribe to index updates for volatility triggers (Polygon)
    /// </summary>
    Task SubscribeToIndexUpdatesAsync(IEnumerable<string> indexSymbols);
    
    /// <summary>
    /// Subscribe to trade updates for portfolio monitoring (Alpaca)
    /// </summary>
    Task SubscribeToTradeUpdatesAsync();
    
    /// <summary>
    /// Unsubscribe from all symbols
    /// </summary>
    Task UnsubscribeAllAsync();
    
    // === Status ===
    
    /// <summary>
    /// Gets the current connection status
    /// </summary>
    StreamingConnectionStatus ConnectionStatus { get; }
}

/// <summary>
/// Event args for streaming quote data
/// </summary>
public class StreamingQuoteEventArgs : EventArgs
{
    public string Symbol { get; init; } = "";
    public decimal BidPrice { get; init; }
    public decimal AskPrice { get; init; }
    public long BidSize { get; init; }
    public long AskSize { get; init; }
    public DateTime Timestamp { get; init; }
}

/// <summary>
/// Event args for streaming bar data
/// </summary>
public class StreamingBarEventArgs : EventArgs
{
    public string Symbol { get; init; } = "";
    public decimal Open { get; init; }
    public decimal High { get; init; }
    public decimal Low { get; init; }
    public decimal Close { get; init; }
    public long Volume { get; init; }
    public DateTime Timestamp { get; init; }
}

/// <summary>
/// Event args for index updates (VIX spikes, etc.)
/// </summary>
public class IndexUpdateEventArgs : EventArgs
{
    public string IndexSymbol { get; init; } = "";
    public decimal Value { get; init; }
    public decimal Change { get; init; }
    public decimal ChangePercent { get; init; }
    public DateTime Timestamp { get; init; }
}

/// <summary>
/// Event args for trade updates/fills
/// </summary>
public class TradeUpdateEventArgs : EventArgs
{
    public string Symbol { get; init; } = "";
    public string OrderId { get; init; } = "";
    public decimal Quantity { get; init; }
    public decimal Price { get; init; }
    public string Side { get; init; } = ""; // "buy" or "sell"
    public string Status { get; init; } = ""; // "filled", "partially_filled", etc.
    public DateTime Timestamp { get; init; }
}

/// <summary>
/// Connection status for streaming services
/// </summary>
public enum StreamingConnectionStatus
{
    Disconnected,
    Connecting,
    Connected,
    Reconnecting,
    Error
}
