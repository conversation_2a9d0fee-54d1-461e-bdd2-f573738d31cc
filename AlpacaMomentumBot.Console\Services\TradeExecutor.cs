using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

public sealed class TradeExecutor : ITradeExecutor
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly IStopManager _stopManager;
    private readonly ILogger<TradeExecutor> _logger;

    public TradeExecutor(IAlpacaClientFactory clientFactory, IStopManager stopManager, ILogger<TradeExecutor> logger)
    {
        _clientFactory = clientFactory;
        _stopManager = stopManager;
        _logger = logger;
    }

    public async Task ExecuteTradeAsync(TradingSignal signal, decimal quantity)
    {
        if (quantity <= 0)
        {
            _logger.LogWarning("Invalid quantity {Quantity} for {Symbol}, skipping trade", quantity, signal.Symbol);
            return;
        }

        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        await rateLimitHelper.ExecuteAsync<object>(async () =>
        {
            try
            {
                using var tradingClient = _clientFactory.CreateTradingClient();

                // Cancel existing orders for this symbol
                await CancelExistingOrdersAsync(tradingClient, signal.Symbol);

                // Calculate entry price: lastClose * 1.002m (Limit-on-Open)
                var entryPrice = signal.Price * 1.002m;

                // Calculate stop-loss price: entry - 2×ATR
                var stopLossPrice = entryPrice - (2m * signal.Atr);

                // Submit Limit-on-Open buy order
                // For now, use whole shares only (round up to ensure we have at least 1 share)
                var orderQuantity = Math.Max(1, (int)Math.Ceiling(quantity));
                var buyOrder = new NewOrderRequest(signal.Symbol, orderQuantity, OrderSide.Buy, OrderType.Limit, TimeInForce.Day)
                {
                    LimitPrice = entryPrice
                };

                _logger.LogInformation("Submitting buy order for {Symbol}: Quantity={Quantity}, OrderType=Limit, LimitPrice={LimitPrice:C}",
                    signal.Symbol, orderQuantity, entryPrice);

                // Add timeout to prevent hanging
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                var submittedOrder = await tradingClient.PostOrderAsync(buyOrder, cts.Token);

                _logger.LogInformation("Successfully submitted buy order for {Symbol}: OrderId={OrderId}, Quantity={Quantity}, LimitPrice={LimitPrice:C}",
                    signal.Symbol, submittedOrder.OrderId, quantity, entryPrice);

                // Set initial trailing stop using StopManager
                // Note: In production, you might want to wait for the buy order to fill before setting the stop
                await _stopManager.SetInitialStopAsync(signal.Symbol, entryPrice, signal.Atr, quantity, cts.Token);
                return (object)null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing trade for {Symbol}", signal.Symbol);
                return (object)null;
            }
        }, $"ExecuteTrade-{signal.Symbol}");
    }

    private async Task CancelExistingOrdersAsync(IAlpacaTradingClient tradingClient, string symbol)
    {
        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        await rateLimitHelper.ExecuteAsync<object>(async () =>
        {
            try
            {
                var openOrders = await tradingClient.ListOrdersAsync(new ListOrdersRequest
                {
                    OrderStatusFilter = OrderStatusFilter.Open,
                    LimitOrderNumber = 100
                });

                var symbolOrders = openOrders.Where(o => o.Symbol == symbol).ToList();

                foreach (var order in symbolOrders)
                {
                    await tradingClient.CancelOrderAsync(order.OrderId, CancellationToken.None);
                    _logger.LogInformation("Cancelled existing order {OrderId} for {Symbol}", order.OrderId, symbol);
                }
                return (object)null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error cancelling existing orders for {Symbol}", symbol);
                return (object)null;
            }
        }, $"CancelOrders-{symbol}");
    }
}
