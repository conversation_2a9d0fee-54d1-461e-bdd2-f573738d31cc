using SmaTrendFollower.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class TradingServiceTests
{
    private readonly Mock<ISignalGenerator> _mockSignalGenerator;
    private readonly Mock<IRiskManager> _mockRiskManager;
    private readonly Mock<IPortfolioGate> _mockPortfolioGate;
    private readonly Mock<ITradeExecutor> _mockTradeExecutor;
    private readonly Mock<IStopManager> _mockStopManager;
    private readonly TradingService _tradingService;

    public TradingServiceTests()
    {
        _mockSignalGenerator = new Mock<ISignalGenerator>();
        _mockRiskManager = new Mock<IRiskManager>();
        _mockPortfolioGate = new Mock<IPortfolioGate>();
        _mockTradeExecutor = new Mock<ITradeExecutor>();
        _mockStopManager = new Mock<IStopManager>();

        _tradingService = new TradingService(
            _mockSignalGenerator.Object,
            _mockRiskManager.Object,
            _mockPortfolioGate.Object,
            _mockTradeExecutor.Object,
            _mockStopManager.Object);
    }

    [Fact]
    public async Task ExecuteCycleAsync_ShouldUpdateTrailingStopsFirst()
    {
        // Arrange
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(It.IsAny<int>())).ReturnsAsync(new List<TradingSignal>());

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockStopManager.Verify(x => x.UpdateTrailingStopsAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExecuteCycleAsync_WhenPortfolioGateBlocksTrading_ShouldNotGenerateSignals()
    {
        // Arrange
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(false);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockStopManager.Verify(x => x.UpdateTrailingStopsAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockSignalGenerator.Verify(x => x.RunAsync(It.IsAny<int>()), Times.Never);
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>()), Times.Never);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.IsAny<TradingSignal>(), It.IsAny<decimal>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteCycleAsync_WhenPortfolioGateAllowsTrading_ShouldGenerateSignals()
    {
        // Arrange
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(new List<TradingSignal>());

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockSignalGenerator.Verify(x => x.RunAsync(10), Times.Once);
    }

    [Fact]
    public async Task ExecuteCycleAsync_WithValidSignals_ShouldCalculateQuantityAndExecuteTrades()
    {
        // Arrange
        var signals = new List<TradingSignal>
        {
            new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m),
            new TradingSignal("MSFT", 300.00m, 5.00m, 0.20m)
        };

        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(signals);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>())).ReturnsAsync(10m);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(It.Is<TradingSignal>(s => s.Symbol == "AAPL")), Times.Once);
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(It.Is<TradingSignal>(s => s.Symbol == "MSFT")), Times.Once);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.Is<TradingSignal>(s => s.Symbol == "AAPL"), 10m), Times.Once);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.Is<TradingSignal>(s => s.Symbol == "MSFT"), 10m), Times.Once);
    }

    [Fact]
    public async Task ExecuteCycleAsync_WithZeroQuantity_ShouldSkipTradeExecution()
    {
        // Arrange
        var signals = new List<TradingSignal>
        {
            new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m)
        };

        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(signals);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>())).ReturnsAsync(0m);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(It.Is<TradingSignal>(s => s.Symbol == "AAPL")), Times.Once);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.IsAny<TradingSignal>(), It.IsAny<decimal>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteCycleAsync_WithMixedQuantities_ShouldOnlyExecuteValidTrades()
    {
        // Arrange
        var signals = new List<TradingSignal>
        {
            new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m),
            new TradingSignal("MSFT", 300.00m, 5.00m, 0.20m),
            new TradingSignal("GOOGL", 2500.00m, 50.00m, 0.25m)
        };

        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(signals);
        
        // Setup different quantities for each signal
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.Is<TradingSignal>(s => s.Symbol == "AAPL"))).ReturnsAsync(10m);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.Is<TradingSignal>(s => s.Symbol == "MSFT"))).ReturnsAsync(0m);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.Is<TradingSignal>(s => s.Symbol == "GOOGL"))).ReturnsAsync(5m);

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.Is<TradingSignal>(s => s.Symbol == "AAPL"), 10m), Times.Once);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.Is<TradingSignal>(s => s.Symbol == "MSFT"), It.IsAny<decimal>()), Times.Never);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.Is<TradingSignal>(s => s.Symbol == "GOOGL"), 5m), Times.Once);
    }

    [Fact]
    public async Task ExecuteCycleAsync_WithNoSignals_ShouldCompleteWithoutErrors()
    {
        // Arrange
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(new List<TradingSignal>());

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        _mockStopManager.Verify(x => x.UpdateTrailingStopsAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockPortfolioGate.Verify(x => x.ShouldTradeAsync(), Times.Once);
        _mockSignalGenerator.Verify(x => x.RunAsync(10), Times.Once);
        _mockRiskManager.Verify(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>()), Times.Never);
        _mockTradeExecutor.Verify(x => x.ExecuteTradeAsync(It.IsAny<TradingSignal>(), It.IsAny<decimal>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteCycleAsync_WithCancellationToken_ShouldPassTokenToStopManager()
    {
        // Arrange
        var cancellationToken = new CancellationToken();
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(false);

        // Act
        await _tradingService.ExecuteCycleAsync(cancellationToken);

        // Assert
        _mockStopManager.Verify(x => x.UpdateTrailingStopsAsync(cancellationToken), Times.Once);
    }

    [Fact]
    public async Task ExecuteCycleAsync_ShouldFollowCorrectExecutionOrder()
    {
        // Arrange
        var signals = new List<TradingSignal>
        {
            new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m)
        };

        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync()).ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(10)).ReturnsAsync(signals);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>())).ReturnsAsync(10m);

        var callOrder = new List<string>();
        _mockStopManager.Setup(x => x.UpdateTrailingStopsAsync(It.IsAny<CancellationToken>()))
            .Callback(() => callOrder.Add("UpdateTrailingStops"));
        _mockPortfolioGate.Setup(x => x.ShouldTradeAsync())
            .Callback(() => callOrder.Add("ShouldTrade"))
            .ReturnsAsync(true);
        _mockSignalGenerator.Setup(x => x.RunAsync(It.IsAny<int>()))
            .Callback(() => callOrder.Add("RunAsync"))
            .ReturnsAsync(signals);
        _mockRiskManager.Setup(x => x.CalculateQuantityAsync(It.IsAny<TradingSignal>()))
            .Callback(() => callOrder.Add("CalculateQuantity"))
            .ReturnsAsync(10m);
        _mockTradeExecutor.Setup(x => x.ExecuteTradeAsync(It.IsAny<TradingSignal>(), It.IsAny<decimal>()))
            .Callback(() => callOrder.Add("ExecuteTrade"));

        // Act
        await _tradingService.ExecuteCycleAsync();

        // Assert
        callOrder.Should().Equal("UpdateTrailingStops", "ShouldTrade", "RunAsync", "CalculateQuantity", "ExecuteTrade");
    }
}
