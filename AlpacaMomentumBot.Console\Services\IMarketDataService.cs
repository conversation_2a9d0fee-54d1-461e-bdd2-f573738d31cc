using Alpaca.Markets;

namespace SmaTrendFollower.Services;

public interface IMarketDataService
{
    // === Historical Data ===

    /// <summary>
    /// Gets historical daily bars for stock/ETF symbols from Alpaca
    /// </summary>
    Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime startDate, DateTime endDate);

    /// <summary>
    /// Gets historical minute bars for stock/ETF symbols from Alpaca
    /// </summary>
    Task<IPage<IBar>> GetStockMinuteBarsAsync(string symbol, DateTime startDate, DateTime endDate);

    /// <summary>
    /// Gets historical bars for multiple stock/ETF symbols from Alpaca
    /// </summary>
    Task<IDictionary<string, IPage<IBar>>> GetStockBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate);

    /// <summary>
    /// Gets historical minute bars for multiple stock/ETF symbols from Alpaca with fallback to Polygon
    /// </summary>
    Task<IDictionary<string, IPage<IBar>>> GetStockMinuteBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate);

    /// <summary>
    /// Gets index-level data (like SPX, VIX) from Polygon
    /// </summary>
    Task<decimal?> GetIndexValueAsync(string indexSymbol);

    /// <summary>
    /// Gets historical index data from Polygon
    /// </summary>
    Task<IEnumerable<IndexBar>> GetIndexBarsAsync(string indexSymbol, DateTime startDate, DateTime endDate);

    // === Account & Positions ===

    /// <summary>
    /// Gets current account information from Alpaca
    /// </summary>
    Task<IAccount> GetAccountAsync();

    /// <summary>
    /// Gets current positions from Alpaca
    /// </summary>
    Task<IReadOnlyList<IPosition>> GetPositionsAsync();

    /// <summary>
    /// Gets recent fills/executions from Alpaca
    /// </summary>
    Task<IReadOnlyList<IOrder>> GetRecentFillsAsync(int limitCount = 100);

    // === Options Data (Polygon) ===

    /// <summary>
    /// Gets options chain data including Greeks, IV, and OI from Polygon
    /// </summary>
    Task<IEnumerable<OptionData>> GetOptionsDataAsync(string underlyingSymbol, DateTime? expirationDate = null);

    /// <summary>
    /// Gets VIX term structure data from Polygon
    /// </summary>
    Task<IEnumerable<VixTermData>> GetVixTermStructureAsync();
}

/// <summary>
/// Represents a bar of index data from Polygon
/// </summary>
public readonly record struct IndexBar(
    DateTime TimeUtc,
    decimal Open,
    decimal High,
    decimal Low,
    decimal Close,
    long Volume
);

/// <summary>
/// Represents options data from Polygon
/// </summary>
public readonly record struct OptionData(
    string Symbol,
    string UnderlyingSymbol,
    DateTime ExpirationDate,
    decimal Strike,
    string OptionType, // "call" or "put"
    decimal? LastPrice,
    decimal? Bid,
    decimal? Ask,
    long? Volume,
    long? OpenInterest,
    decimal? ImpliedVolatility,
    decimal? Delta,
    decimal? Gamma,
    decimal? Theta,
    decimal? Vega
);

/// <summary>
/// Represents VIX term structure data
/// </summary>
public readonly record struct VixTermData(
    DateTime ExpirationDate,
    decimal Price,
    int DaysToExpiration
);
